const fs = require('fs');
const path = require('path');

// Read the JSON report
const report = JSON.parse(fs.readFileSync('js_definitions_report.json', 'utf8'));

// Get all files with definitions
const filesWithDefinitions = new Set([
    ...report.details.ccclasses.map(c => c.file),
    ...report.details.staticClasses.map(c => c.file),
    ...report.details.exportedFunctions.map(f => f.file),
    ...report.details.enums.map(e => e.file),
    ...report.details.otherDefinitions.map(d => d.file)
]);

// Get all JS files
function getAllJSFiles(dirPath) {
    const files = [];
    
    function traverse(currentPath) {
        const items = fs.readdirSync(currentPath);
        
        items.forEach(item => {
            if (item.endsWith('.meta')) return; // Skip meta files
            
            const fullPath = path.join(currentPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                traverse(fullPath);
            } else if (item.endsWith('.js')) {
                files.push(fullPath);
            }
        });
    }
    
    traverse(dirPath);
    return files;
}

const allFiles = getAllJSFiles('assets/_script');
const filesWithoutDefinitions = [];

allFiles.forEach(file => {
    const relativePath = path.relative('assets/_script', file);
    if (!filesWithDefinitions.has(relativePath)) {
        filesWithoutDefinitions.push(relativePath);
    }
});

console.log(`总文件数: ${allFiles.length}`);
console.log(`包含定义的文件数: ${filesWithDefinitions.size}`);
console.log(`未识别定义的文件数: ${filesWithoutDefinitions.length}`);

// 分析未识别的文件
console.log('\n未识别定义的文件样本 (前20个):');
filesWithoutDefinitions.slice(0, 20).forEach(file => {
    console.log(`- ${file}`);
});

// 分析这些文件的内容特征
console.log('\n分析未识别文件的内容特征...');
const contentAnalysis = {
    empty: 0,
    minified: 0,
    compiled: 0,
    simple: 0,
    other: 0
};

filesWithoutDefinitions.slice(0, 50).forEach(file => {
    try {
        const content = fs.readFileSync(path.join('assets/_script', file), 'utf8');
        
        if (content.trim().length === 0) {
            contentAnalysis.empty++;
        } else if (content.length > 1000 && content.split('\n').length < 10) {
            contentAnalysis.minified++;
        } else if (content.includes('cc__extends') || content.includes('cc__decorate')) {
            contentAnalysis.compiled++;
        } else if (content.split('\n').length < 50) {
            contentAnalysis.simple++;
        } else {
            contentAnalysis.other++;
        }
    } catch (error) {
        console.log(`Error reading ${file}: ${error.message}`);
    }
});

console.log('\n内容特征分析 (前50个文件):');
console.log(`- 空文件: ${contentAnalysis.empty}`);
console.log(`- 压缩文件: ${contentAnalysis.minified}`);
console.log(`- 编译后文件: ${contentAnalysis.compiled}`);
console.log(`- 简单文件: ${contentAnalysis.simple}`);
console.log(`- 其他: ${contentAnalysis.other}`);

// 保存完整的未识别文件列表
fs.writeFileSync('files_without_definitions.txt', filesWithoutDefinitions.join('\n'));
console.log('\n完整的未识别文件列表已保存到: files_without_definitions.txt');
