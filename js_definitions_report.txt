================================================================================
                    JavaScript 脚本定义分析报告
================================================================================

分析概要:
- 总文件数: 344
- 包含定义的文件数: 329
- CCClass 组件类: 342
- 静态类/管理器: 23
- 导出函数: 25
- 枚举类型: 15
- 其他定义: 339
- 未识别文件数: 15

1. CCClass 组件类 (继承关系)
--------------------------------------------------
- exp_activityCfgReader extends e (activityCfg.js)
- exp_ADController extends e (ADController.js)
- ADModel extends e (ADModel.js)
- exp_adRewardCfgReader extends e (adRewardCfg.js)
- ArcBullet extends e (ArcBullet.js)
- AutoAmTool extends e (AutoAmTool.js)
- AutoAnimationClip extends e (AutoAnimationClip.js)
- AutoFollow extends e (AutoFollow.js)
- BackHeroProp extends e (BackHeroProp.js)
- BackpackHeroHome extends e (BackpackHeroHome.js)
- exp_BagBuffCfgReader extends e (BagBuffCfg.js)
- exp_BagGuideCfgReader extends e (BagGuideCfg.js)
- exp_BagModeLvCfgReader extends e (BagModeLvCfg.js)
- exp_BagModeSkillPoolCfgReader extends e (BagModeSkillPoolCfg.js)
- exp_bagMonsterLvCfgReader extends e (bagMonsterLvCfg.js)
- exp_BagShopItemCfgReader extends e (BagShopItemCfg.js)
- exp_BagSkillCfgReader extends e (BagSkillCfg.js)
- BaseEntity (BaseEntity.js)
- BoomerangBullet extends e (BoomerangBullet.js)
- exp_BottomBarController extends e (BottomBarController.js)
- BottomBarModel extends e (BottomBarModel.js)
- BottomBarView extends e (BottomBarView.js)
- exp_BottomBarView extends e (BottomBarView.js)
- BounceBullet extends e (BounceBullet.js)
- exp_BoxLevelExpCfgReader extends e (BoxLevelExpCfg.js)
- exp_BronMonsterManger extends e (BronMonsterManger.js)
- BuffCardItem extends e (BuffCardItem.js)
- exp_BuffCfgReader extends e (BuffCfg.js)
- exp_BuffController extends e (BuffController.js)
- BuffList extends e (BuffList.js)
- exp_Buff_Default extends e (BuffList.js)
- exp_Buff_Excute extends e (BuffList.js)
- exp_Buff_OnTime extends e (BuffList.js)
- exp_Buff_Effect extends e (BuffList.js)
- exp_Buff_OnSpawnHurt extends e (BuffList.js)
- exp_Buff_OnVampirism extends e (BuffList.js)
- exp_Buff_CurrencyReward extends e (BuffList.js)
- exp_Buff_OnBehit extends e (BuffList.js)
- exp_Buff_OnKill extends e (BuffList.js)
- exp_Buff_HPLink extends e (BuffList.js)
- exp_Buff_ContinuousRecovery extends e (BuffList.js)
- exp_Buff_HPLinkOnce extends e (BuffList.js)
- exp_Buff_EntityDead extends e (BuffList.js)
- exp_Buff_VicinityHurt extends e (BuffList.js)
- exp_Buff_Halo extends e (BuffList.js)
- exp_Buff_Hurt extends e (BuffList.js)
- exp_Buff_OnUseSkillHurt extends e (BuffList.js)
- exp_Buff_SubSkill extends e (BuffList.js)
- exp_Buff_AtkFocus extends e (BuffList.js)
- exp_Buff_AdrenalTechnology extends e (BuffList.js)
- exp_Buff_OnSkillUseUnload extends e (BuffList.js)
- exp_Buff_ReboundDam extends e (BuffList.js)
- exp_Buff_OnKillLayout extends e (BuffList.js)
- exp_Buff_HitBack extends e (BuffList.js)
- exp_Buff_OnLifeVal extends e (BuffList.js)
- exp_Buff_OnSpawnHurtAddArmor extends e (BuffList.js)
- exp_Buff_OnBehitAddArmor extends e (BuffList.js)
- exp_Buff_RestoreArmor extends e (BuffList.js)
- exp_Buff_OnSkill extends e (BuffList.js)
- exp_Buff_Vampire extends e (BuffList.js)
- exp_Buff_AssociationProp extends e (BuffList.js)
- exp_Buff_ResistDamage extends e (BuffList.js)
- exp_Buff_SetSlash extends e (BuffList.js)
- exp_Buff_OnRoundState extends e (BuffList.js)
- exp_Buff_ReplaceRole extends e (BuffList.js)
- BuffModel extends e (BuffModel.js)
- exp_BuildModeSkiilpoolCfgReader extends e (BuildModeSkiilpoolCfg.js)
- Bullet extends e (Bullet.js)
- BulletBase extends e (BulletBase.js)
- exp_BulletEffectCfgReader extends e (BulletEffectCfg.js)
- Bullet_Arrow extends e (Bullet_Arrow.js)
- Bullet_FollowTarget extends e (Bullet_FollowTarget.js)
- Bullet_HitReflex extends e (Bullet_HitReflex.js)
- Bullet_Laser extends e (Bullet_Laser.js)
- Bullet_Ligature extends e (Bullet_Ligature.js)
- Bullet_LigaturePonit extends e (Bullet_LigaturePonit.js)
- Bullet_Path extends e (Bullet_Path.js)
- Bullet_RandomMove extends e (Bullet_RandomMove.js)
- Bullet_RigidBody extends e (Bullet_RigidBody.js)
- ByteDance extends e (ByteDance.js)
- CircleBullet extends e (CircleBullet.js)
- Commonguide (Commonguide.js)
- ContinuousBullet extends e (ContinuousBullet.js)
- exp_CurrencyConfigCfgReader extends e (CurrencyConfigCfg.js)
- CurrencyTips extends e (CurrencyTips.js)
- DialogBox extends e (DialogBox.js)
- exp_dmmItemCfgReader extends e (dmmItemCfg.js)
- exp_dmmRoleCfgReader extends e (dmmRoleCfg.js)
- Dragon extends e (Dragon.js)
- exp_Dragon extends e (Dragon.js)
- DragonBody extends e (DragonBody.js)
- exp_dragonPathCfgReader extends e (dragonPathCfg.js)
- exp_DropConfigCfgReader extends e (DropConfigCfg.js)
- exp_EaseScaleTransition extends e (EaseScaleTransition.js)
- EffectSkeleton extends e (EffectSkeleton.js)
- Effect_Behead extends e (Effect_Behead.js)
- Effect_Behit extends e (Effect_Behit.js)
- EnergyStamp (EnergyStamp.js)
- EntityDieEffect extends e (EntityDieEffect.js)
- exp_EquipLvCfgReader extends e (EquipLvCfg.js)
- exp_EquipMergeLvCfgReader extends e (EquipMergeLvCfg.js)
- exp_EventController extends e (EventController.js)
- EventModel extends e (EventModel.js)
- ExchangeCodeView extends e (ExchangeCodeView.js)
- ExSprite extends e (ExSprite.js)
- FBoxCollider extends e (FBoxCollider.js)
- FCircleCollider extends e (FCircleCollider.js)
- FCollider (FCollider.js)
- exp_FightController extends e (FightController.js)
- FightModel extends e (FightModel.js)
- FightScene extends e (FightScene.js)
- exp_FightScene extends e (FightScene.js)
- FightUIView extends e (FightUIView.js)
- exp_FightUIView extends e (FightUIView.js)
- FPolygonCollider extends e (FPolygonCollider.js)
- GameAnimi extends e (GameAnimi.js)
- exp_GameatrCfgReader extends e (GameatrCfg.js)
- exp_GameCamera extends e (GameCamera.js)
- GameEffect extends e (GameEffect.js)
- exp_GameSettingCfgReader extends e (GameSettingCfg.js)
- GameSkeleton (GameSkeleton.js)
- Goods extends e (Goods.js)
- GoodsUIItem (GoodsUIItem.js)
- GridView extends e (GridView.js)
- GridViewCell extends e (GridViewCell.js)
- GTAssembler2D extends e (GTAssembler2D.js)
- GTSimpleSpriteAssembler2D extends e (GTSimpleSpriteAssembler2D.js)
- exp_GuideCfgReader extends e (GuideCfg.js)
- exp_GuidesController extends e (GuidesController.js)
- GuidesModel extends e (GuidesModel.js)
- IOSSdk extends e (IOSSdk.js)
- exp_ItemController extends e (ItemController.js)
- ItemModel extends e (ItemModel.js)
- JUHEAndroid extends e (JUHEAndroid.js)
- KawaseAnim extends e (KawaseAnim.js)
- exp_languageCfgReader extends e (languageCfg.js)
- LaserRadiationBullet extends e (LaserRadiationBullet.js)
- Launcher extends e (Launcher.js)
- exp_LevelExpCfgReader extends e (LevelExpCfg.js)
- LevelMgr extends e (LevelMgr.js)
- LifeBar extends e (LifeBar.js)
- LifeLabel extends e (LifeLabel.js)
- LigatureBullet extends e (LigatureBullet.js)
- exp_LoadingController extends e (LoadingController.js)
- LoadingModel extends e (LoadingModel.js)
- LoadingView extends e (LoadingView.js)
- exp_LoadingView extends e (LoadingView.js)
- exp_LvInsideCfgReader extends e (LvInsideCfg.js)
- exp_LvOutsideCfgReader extends e (LvOutsideCfg.js)
- M20Equipitem extends e (M20Equipitem.js)
- M20EquipitemBlock extends e (M20EquipitemBlock.js)
- M20EquipitemList extends e (M20EquipitemList.js)
- M20Gooditem extends e (M20Gooditem.js)
- M20Prop extends e (M20Prop.js)
- M20Prop_Equip (M20Prop_Equip.js)
- M20Prop_Gemstone (M20Prop_Gemstone.js)
- M20_PartItem extends e (M20_PartItem.js)
- M20_Pop_EquipInfo extends e (M20_Pop_EquipInfo.js)
- M20_Pop_GameRewardView extends e (M20_Pop_GameRewardView.js)
- M20_Pop_GetBox extends e (M20_Pop_GetBox.js)
- M20_Pop_GetEnergy extends e (M20_Pop_GetEnergy.js)
- M20_Pop_Insufficient_Props_Tips extends e (M20_Pop_Insufficient_Props_Tips.js)
- M20_Pop_NewEquipUnlock extends e (M20_Pop_NewEquipUnlock.js)
- M20_Pop_ShopBoxInfo extends e (M20_Pop_ShopBoxInfo.js)
- M20_Pop_ShopBuyConfirm extends e (M20_Pop_ShopBuyConfirm.js)
- M20_PrePare_Activity extends e (M20_PrePare_Activity.js)
- M20_PrePare_Equip extends e (M20_PrePare_Equip.js)
- M20_PrePare_Fight extends e (M20_PrePare_Fight.js)
- M20_PrePare_MenuView extends e (M20_PrePare_MenuView.js)
- M20_PrePare_Shop extends e (M20_PrePare_Shop.js)
- M20_ShopPartItem extends e (M20_ShopPartItem.js)
- M20_ShopPartItem_adcoupon extends e (M20_ShopPartItem_adcoupon.js)
- M20_ShopPartItem_box extends e (M20_ShopPartItem_box.js)
- M20_ShopPartItem_coin extends e (M20_ShopPartItem_coin.js)
- M20_ShopPartItem_daily extends e (M20_ShopPartItem_daily.js)
- M20_ShopPartItem_hero extends e (M20_ShopPartItem_hero.js)
- M20_Shop_HeroItem extends e (M20_Shop_HeroItem.js)
- M33_FightBuffView extends e (M33_FightBuffView.js)
- M33_FightScene extends e (M33_FightScene.js)
- exp_M33_FightScene extends e (M33_FightScene.js)
- M33_FightUIView extends e (M33_FightUIView.js)
- exp_M33_FightUIView extends e (M33_FightUIView.js)
- M33_Pop_DiffSelectGeneral extends e (M33_Pop_DiffSelectGeneral.js)
- M33_Pop_GameEnd extends e (M33_Pop_GameEnd.js)
- M33_Pop_Revive extends e (M33_Pop_Revive.js)
- M33_TestBox extends e (M33_TestBox.js)
- exp_MapCfgReader extends e (MapCfg.js)
- MBRMonster extends e (MBRMonster.js)
- MBRRole extends e (MBRRole.js)
- MCBoss (MCBoss.js)
- exp_MCBoss extends e (MCBoss.js)
- MCBossState extends e (MCBossState.js)
- MCDragoMutilation extends e (MCDragoMutilation.js)
- exp_MCDragoMutilation extends e (MCDragoMutilation.js)
- MCDragon extends e (MCDragon.js)
- exp_MCDragon extends e (MCDragon.js)
- MCPet extends e (MCPet.js)
- MCRole extends e (MCRole.js)
- exp_MiniGameEquipCfgReader extends e (MiniGameEquipCfg.js)
- exp_MiniGameLvCfgReader extends e (MiniGameLvCfg.js)
- MMGMonster extends e (MMGMonster.js)
- MMGRole extends e (MMGRole.js)
- MMGuards extends e (MMGuards.js)
- exp_ModeAllOutAttackController extends e (ModeAllOutAttackController.js)
- ModeAllOutAttackModel extends e (ModeAllOutAttackModel.js)
- exp_ModeBackpackHeroController extends e (ModeBackpackHeroController.js)
- ModeBackpackHeroModel extends e (ModeBackpackHeroModel.js)
- exp_ModeBulletsReboundController extends e (ModeBulletsReboundController.js)
- ModeBulletsReboundModel extends e (ModeBulletsReboundModel.js)
- exp_ModeChainsController extends e (ModeChainsController.js)
- ModeChainsModel extends e (ModeChainsModel.js)
- exp_ModeDragonWarController extends e (ModeDragonWarController.js)
- ModeDragonWarModel extends e (ModeDragonWarModel.js)
- exp_ModeManGuardsController extends e (ModeManGuardsController.js)
- ModeManGuardsModel extends e (ModeManGuardsModel.js)
- exp_ModePickUpBulletsController extends e (ModePickUpBulletsController.js)
- ModePickUpBulletsModel extends e (ModePickUpBulletsModel.js)
- exp_ModeThrowingKnifeController extends e (ModeThrowingKnifeController.js)
- ModeThrowingKnifeModel extends e (ModeThrowingKnifeModel.js)
- MonstarTideDragon extends e (MonstarTideDragon.js)
- exp_Monster extends e (Monster.js)
- exp_MonsterCfgReader extends e (MonsterCfg.js)
- MonsterElite extends e (MonsterElite.js)
- exp_MonsterLvCfgReader extends e (MonsterLvCfg.js)
- MonsterState extends e (MonsterState.js)
- MonsterTidal extends e (MonsterTidal.js)
- MonsterTidalBoss extends e (MonsterTidalBoss.js)
- MonsterTidalState extends e (MonsterTidalState.js)
- MonsterTideDefend extends e (MonsterTideDefend.js)
- MoreGamesItem extends e (MoreGamesItem.js)
- MoreGamesView extends e (MoreGamesView.js)
- MoveEntity extends e (MoveEntity.js)
- MoveImg extends e (MoveImg.js)
- MovingBGAssembler extends e (MovingBGAssembler.js)
- MovingBGSprite extends e (MovingBGSprite.js)
- MTideDefendRmod extends e (MTideDefendRmod.js)
- MTKnife extends t (MTKnife.js)
- MTKRole extends e (MTKRole.js)
- MVC extends e (MVC.js)
- NativeAndroid extends e (NativeAndroid.js)
- NormalTips extends e (NormalTips.js)
- NPC extends e (NPC.js)
- OrganismBase extends e (OrganismBase.js)
- exp_PayController extends e (PayController.js)
- PayModel extends e (PayModel.js)
- exp_PayShopCfgReader extends e (PayShopCfg.js)
- Pet extends e (Pet.js)
- PetState extends e (PetState.js)
- exp_PoolListCfgReader extends e (PoolListCfg.js)
- Pop extends e (Pop.js)
- exp_Pop extends e (Pop.js)
- exp_ProcessRewardsCfgReader extends e (ProcessRewardsCfg.js)
- PropertyVo extends e (PropertyVo.js)
- exp_randomNameCfgReader extends e (randomNameCfg.js)
- exp_RBadgeController extends e (RBadgeController.js)
- RBadgeModel extends e (RBadgeModel.js)
- RBadgePoint extends e (RBadgePoint.js)
- ReflexBullet extends e (ReflexBullet.js)
- ResKeeper extends e (ResKeeper.js)
- Role extends e (Role.js)
- exp_RoleCfgReader extends e (RoleCfg.js)
- exp_RoleLvCfgReader extends e (RoleLvCfg.js)
- RoleSkillList extends e (RoleSkillList.js)
- exp_Skill_Default extends e (RoleSkillList.js)
- exp_Skill_AtkDefault extends e (RoleSkillList.js)
- exp_Skill_NormalChop extends e (RoleSkillList.js)
- exp_Skill_MagicBall extends e (RoleSkillList.js)
- exp_Skill_Multishot extends e (RoleSkillList.js)
- exp_Skill_FireOffset extends e (RoleSkillList.js)
- exp_Skill_ParallelFire extends e (RoleSkillList.js)
- exp_Skill_Sniper extends e (RoleSkillList.js)
- exp_Skill_MultiBullet extends e (RoleSkillList.js)
- exp_Skill_StruckLightning extends e (RoleSkillList.js)
- exp_Skill_Meteorite extends e (RoleSkillList.js)
- exp_Skill_RingFireballs extends e (RoleSkillList.js)
- exp_Skill_RangeBomb extends e (RoleSkillList.js)
- exp_Skill_LaserRadiation extends e (RoleSkillList.js)
- exp_Skill_LaserAnacampsis extends e (RoleSkillList.js)
- exp_Skill_Ligature extends e (RoleSkillList.js)
- exp_Skill_LaserRadiationGuard extends e (RoleSkillList.js)
- exp_Skill_Arrows extends e (RoleSkillList.js)
- exp_Skill_Icicle extends e (RoleSkillList.js)
- exp_Skill_BounceThrowingKnife extends e (RoleSkillList.js)
- exp_Skill_Whirlwind extends e (RoleSkillList.js)
- exp_Skill_Tornado extends e (RoleSkillList.js)
- exp_Skill_Continuous extends e (RoleSkillList.js)
- exp_Skill_GoldenCudgel extends e (RoleSkillList.js)
- exp_Skill_SwordSword extends e (RoleSkillList.js)
- exp_Skill_EffectSkill extends e (RoleSkillList.js)
- exp_Skill_ColdAir extends e (RoleSkillList.js)
- exp_Skill_PosExcute extends e (RoleSkillList.js)
- exp_Skill_StampSweep extends e (RoleSkillList.js)
- exp_Skill_PowerStorage extends e (RoleSkillList.js)
- exp_Skill_Flower extends e (RoleSkillList.js)
- exp_Skill_FollowPath extends e (RoleSkillList.js)
- exp_Skill_MCDragonFollowPath extends e (RoleSkillList.js)
- RoleState extends e (RoleState.js)
- exp_RoleUnlockCfgReader extends e (RoleUnlockCfg.js)
- SelectAlert extends e (SelectAlert.js)
- exp_SettingController extends e (SettingController.js)
- SettingModel extends e (SettingModel.js)
- SettingView extends e (SettingView.js)
- exp_SettingView extends e (SettingView.js)
- exp_ShopController extends e (ShopController.js)
- ShopModel extends e (ShopModel.js)
- exp_signCfgReader extends e (signCfg.js)
- SkeletonBullet extends e (SkeletonBullet.js)
- exp_SkiilpoolCfgReader extends e (SkiilpoolCfg.js)
- exp_SkillCfgReader extends e (SkillCfg.js)
- exp_SkillController extends e (SkillController.js)
- SkillModel extends e (SkillModel.js)
- SkillModule (SkillModule.js)
- exp_SoundCfgReader extends e (SoundCfg.js)
- exp_TaskCfgReader extends e (TaskCfg.js)
- TaskModel extends e (TaskModel.js)
- exp_TaskTypeCfgReader extends e (TaskTypeCfg.js)
- exp_TestController extends e (TestController.js)
- TestItem extends e (TestItem.js)
- TestModel extends e (TestModel.js)
- TestView extends e (TestView.js)
- exp_TestView extends e (TestView.js)
- ThrowBullet extends e (ThrowBullet.js)
- exp_TideDefendController extends e (TideDefendController.js)
- TideDefendModel extends e (TideDefendModel.js)
- TornadoBullet extends e (TornadoBullet.js)
- exp_TowerAmethystRewardCfgReader extends e (TowerAmethystRewardCfg.js)
- exp_TowerCfgReader extends e (TowerCfg.js)
- exp_TowerCoinRewardCfgReader extends e (TowerCoinRewardCfg.js)
- exp_TowerLvCfgReader extends e (TowerLvCfg.js)
- exp_TowerMenuCfgReader extends e (TowerMenuCfg.js)
- TrackBullet extends e (TrackBullet.js)
- TrackItem extends e (TrackItem.js)
- exp_TwoDHorizontalLayoutObject extends e (TwoDHorizontalLayoutObject.js)
- exp_TwoDLayoutObject extends e (TwoDLayoutObject.js)
- Vehicle extends e (Vehicle.js)
- VideoButton extends e (VideoButton.js)
- VideoIcon extends e (VideoIcon.js)
- VisibleComponent extends e (VisibleComponent.js)
- WallBase extends e (WallBase.js)
- Weather extends e (Weather.js)
- exp_WeatherCfgReader extends e (WeatherCfg.js)
- WebDev extends e (WebDev.js)

2. 静态类/管理器类
--------------------------------------------------

MANAGER 类型:
  - AlertManager (AlertManager.js)
  - AudioManager (AudioManager.js)
  - CompManager (CompManager.js)
  - FColliderManager (FColliderManager.js)
  - GameUtil (GameUtil.js)
  - Manager (Manager.js)
  - MathUtils (MathUtils.js)
  - NetManager (NetManager.js)
  - ResUtil (ResUtil.js)
  - SkillManager (SkillManager.js)
  - StorageManager (StorageManager.js)
  - UIManager (UIManager.js)
  - VoManager (VoManager.js)

SINGLETON 类型:
  - BronMonsterManger (BronMonsterManger.js)
  - FightController (FightController.js)
  - FightModel (FightModel.js)
  - MVC (MVC.js)
  - NPC (NPC.js)
  - PayController (PayController.js)
  - PayModel (PayModel.js)
  - Role (Role.js)
  - SkillController (SkillController.js)
  - SkillModel (SkillModel.js)

3. 直接导出的函数
--------------------------------------------------
Api.js:
  - click()
  - report()
  - getOpenid()
  - serverTime()
cc_language.js:
  - initlang()
function.js:
  - versionFormat()
  - formatDate()
  - response()
  - getSign()
  - getServerTime()
GameUtil.js:
  - cacheFunction()
  - getClassByName()
Global.js:
  - isNullOrEmpty()
  - copy()
  - toArray()
  - GameDeepCopy()
IOSSdk.js:
  - iOSSendMsg()
  - iOSBuySendMsg()
md51.js:
  - md5()
ModeBackpackHeroModel.js:
  - ActivityPass()
ModuleLauncher.js:
  - ModuleLauncher()
thinkingdata.mg.cocoscreator.min.js:
  - onpagehide()
  - onbeforeunload()
  - __dynamicPropertiesForNative()
UILauncher.js:
  - UILauncher()

4. 枚举类型
--------------------------------------------------
AlertManager.js:
  - AlertType
  - ItemAlertType
AudioManager.js:
  - AudioType
  - PlayType
BaseEntity.js:
  - CampType
  - EntityType
FCollider.js:
  - ColliderType
GridView.js:
  - GRID_TYPE
MoreGamesView.js:
  - MoreGames
NodePool.js:
  - NodeState
PropertyVo.js:
  - Hurt
RBadgeModel.js:
  - RBadge
TaskModel.js:
  - TaskSaveType
TwoDLayoutObject.js:
  - LAYOUT_HORIZONTAL_TYPE
  - LAYOUT_VERTICAL_TYPE

5. 其他定义
--------------------------------------------------

NAMESPACE 类型 (41个):
  - BaseUrl (BaseNet.js)
  - Url (BaseNet.js)
  - VideoAdCode (BaseSdk.js)
  - ShareType (BaseSdk.js)
  - Buff (Buff.js)
  - CallID (CallID.js)
  - RedPointID (CallID.js)
  - CurrencyConfigDefine (CurrencyConfigCfg.js)
  - DropConfigDefine (DropConfigCfg.js)
  - StateType (FCollider.js)
  - Game (Game.js)
  - GameatrDefine (GameatrCfg.js)
  - GameSeting (GameSeting.js)
  - GameSettingDefine (GameSettingCfg.js)
  - CCTool (GameUtil.js)
  - KnapsackVo (KnapsackVo.js)
  - LatticeMap (LatticeMap.js)
  - Level (LevelMgr.js)
  - ListenID (ListenID.js)
  - MBPack (MBackpackHero.js)
  ... 还有 21 个

EXPORTEDCLASS 类型 (212个):
  - activityCfgReader (activityCfg.js)
  - ADController (ADController.js)
  - adRewardCfgReader (adRewardCfg.js)
  - AlertManager (AlertManager.js)
  - LoadResArgs (AssetLoader.js)
  - AudioManager (AudioManager.js)
  - AutoScaleComponent (AutoScaleComponent.js)
  - BagBuffCfgReader (BagBuffCfg.js)
  - BagGuideCfgReader (BagGuideCfg.js)
  - BagModeLvCfgReader (BagModeLvCfg.js)
  - BagModeSkillPoolCfgReader (BagModeSkillPoolCfg.js)
  - bagMonsterLvCfgReader (bagMonsterLvCfg.js)
  - BagShopItemCfgReader (BagShopItemCfg.js)
  - BagSkillCfgReader (BagSkillCfg.js)
  - BaseNet (BaseNet.js)
  - BaseSdk (BaseSdk.js)
  - BottomBarController (BottomBarController.js)
  - BottomBarView (BottomBarView.js)
  - BoxLevelExpCfgReader (BoxLevelExpCfg.js)
  - BronMonsterManger (BronMonsterManger.js)
  ... 还有 192 个

CONFIG 类型 (53个):
  - activityCfg (activityCfg.js)
  - adRewardCfg (adRewardCfg.js)
  - BagBuffCfg (BagBuffCfg.js)
  - BagGuideCfg (BagGuideCfg.js)
  - BagModeLvCfg (BagModeLvCfg.js)
  - BagModeSkillPoolCfg (BagModeSkillPoolCfg.js)
  - bagMonsterLvCfg (bagMonsterLvCfg.js)
  - BagShopItemCfg (BagShopItemCfg.js)
  - BagSkillCfg (BagSkillCfg.js)
  - BoxLevelExpCfg (BoxLevelExpCfg.js)
  - BuffCfg (BuffCfg.js)
  - BuildModeSkiilpoolCfg (BuildModeSkiilpoolCfg.js)
  - BulletEffectCfg (BulletEffectCfg.js)
  - Cfg (Cfg.js)
  - CurrencyConfigCfg (CurrencyConfigCfg.js)
  - dmmItemCfg (dmmItemCfg.js)
  - dmmRoleCfg (dmmRoleCfg.js)
  - dragonPathCfg (dragonPathCfg.js)
  - DropConfigCfg (DropConfigCfg.js)
  - EquipLvCfg (EquipLvCfg.js)
  ... 还有 33 个

VALUEOBJECT 类型 (25个):
  - ADModel (ADModel.js)
  - BottomBarModel (BottomBarModel.js)
  - BuffModel (BuffModel.js)
  - EventModel (EventModel.js)
  - FightModel (FightModel.js)
  - GuidesModel (GuidesModel.js)
  - ItemModel (ItemModel.js)
  - LoadingModel (LoadingModel.js)
  - ModeAllOutAttackModel (ModeAllOutAttackModel.js)
  - ModeBackpackHeroModel (ModeBackpackHeroModel.js)
  - ModeBulletsReboundModel (ModeBulletsReboundModel.js)
  - ModeChainsModel (ModeChainsModel.js)
  - ModeDragonWarModel (ModeDragonWarModel.js)
  - ModeManGuardsModel (ModeManGuardsModel.js)
  - ModePickUpBulletsModel (ModePickUpBulletsModel.js)
  - ModeThrowingKnifeModel (ModeThrowingKnifeModel.js)
  - PayModel (PayModel.js)
  - PropertyVo (PropertyVo.js)
  - RBadgeModel (RBadgeModel.js)
  - SettingModel (SettingModel.js)
  ... 还有 5 个

VIEW 类型 (7个):
  - ExchangeCodeView (ExchangeCodeView.js)
  - GridView (GridView.js)
  - GridViewCell (GridViewCell.js)
  - M20_Pop_GameRewardView (M20_Pop_GameRewardView.js)
  - M20_PrePare_MenuView (M20_PrePare_MenuView.js)
  - M33_FightBuffView (M33_FightBuffView.js)
  - MoreGamesView (MoreGamesView.js)

UTILITY 类型 (1个):
  - AutoAmTool (AutoAmTool.js)
