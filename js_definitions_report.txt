================================================================================
                    JavaScript 脚本定义分析报告
================================================================================

分析概要:
- 总文件数: 169
- CCClass 组件类: 18
- 静态类/管理器: 23
- 导出函数: 25
- 枚举类型: 15
- 其他定义: 125

1. CCClass 组件类 (继承关系)
--------------------------------------------------
- BaseEntity (BaseEntity.js)
- Commonguide (Commonguide.js)
- EnergyStamp (EnergyStamp.js)
- FCollider (FCollider.js)
- GameSkeleton (GameSkeleton.js)
- GoodsUIItem (GoodsUIItem.js)
- LevelMgr extends e (LevelMgr.js)
- M20Prop_Equip (M20Prop_Equip.js)
- M20Prop_Gemstone (M20Prop_Gemstone.js)
- MCBoss (MCBoss.js)
- MCBossState extends e (MCBossState.js)
- MMGuards extends e (MMGuards.js)
- MonsterState extends e (MonsterState.js)
- MonsterTidalState extends e (MonsterTidalState.js)
- MVC extends e (MVC.js)
- PetState extends e (PetState.js)
- PropertyVo extends e (PropertyVo.js)
- RoleState extends e (RoleState.js)

2. 静态类/管理器类
--------------------------------------------------

MANAGER 类型:
  - AlertManager (AlertManager.js)
  - AudioManager (AudioManager.js)
  - CompManager (CompManager.js)
  - FColliderManager (FColliderManager.js)
  - GameUtil (GameUtil.js)
  - Manager (Manager.js)
  - MathUtils (MathUtils.js)
  - NetManager (NetManager.js)
  - ResUtil (ResUtil.js)
  - SkillManager (SkillManager.js)
  - StorageManager (StorageManager.js)
  - UIManager (UIManager.js)
  - VoManager (VoManager.js)

SINGLETON 类型:
  - BronMonsterManger (BronMonsterManger.js)
  - FightController (FightController.js)
  - FightModel (FightModel.js)
  - MVC (MVC.js)
  - NPC (NPC.js)
  - PayController (PayController.js)
  - PayModel (PayModel.js)
  - Role (Role.js)
  - SkillController (SkillController.js)
  - SkillModel (SkillModel.js)

3. 直接导出的函数
--------------------------------------------------
Api.js:
  - click()
  - report()
  - getOpenid()
  - serverTime()
cc_language.js:
  - initlang()
function.js:
  - versionFormat()
  - formatDate()
  - response()
  - getSign()
  - getServerTime()
GameUtil.js:
  - cacheFunction()
  - getClassByName()
Global.js:
  - isNullOrEmpty()
  - copy()
  - toArray()
  - GameDeepCopy()
IOSSdk.js:
  - iOSSendMsg()
  - iOSBuySendMsg()
md51.js:
  - md5()
ModeBackpackHeroModel.js:
  - ActivityPass()
ModuleLauncher.js:
  - ModuleLauncher()
thinkingdata.mg.cocoscreator.min.js:
  - onpagehide()
  - onbeforeunload()
  - __dynamicPropertiesForNative()
UILauncher.js:
  - UILauncher()

4. 枚举类型
--------------------------------------------------
AlertManager.js:
  - AlertType
  - ItemAlertType
AudioManager.js:
  - AudioType
  - PlayType
BaseEntity.js:
  - CampType
  - EntityType
FCollider.js:
  - ColliderType
GridView.js:
  - GRID_TYPE
MoreGamesView.js:
  - MoreGames
NodePool.js:
  - NodeState
PropertyVo.js:
  - Hurt
RBadgeModel.js:
  - RBadge
TaskModel.js:
  - TaskSaveType
TwoDLayoutObject.js:
  - LAYOUT_HORIZONTAL_TYPE
  - LAYOUT_VERTICAL_TYPE

5. 其他定义
--------------------------------------------------

CONFIG 类型:
  - activityCfg (activityCfg.js)
  - adRewardCfg (adRewardCfg.js)
  - BagBuffCfg (BagBuffCfg.js)
  - BagGuideCfg (BagGuideCfg.js)
  - BagModeLvCfg (BagModeLvCfg.js)
  - BagModeSkillPoolCfg (BagModeSkillPoolCfg.js)
  - bagMonsterLvCfg (bagMonsterLvCfg.js)
  - BagShopItemCfg (BagShopItemCfg.js)
  - BagSkillCfg (BagSkillCfg.js)
  - BoxLevelExpCfg (BoxLevelExpCfg.js)
  - BuffCfg (BuffCfg.js)
  - BuildModeSkiilpoolCfg (BuildModeSkiilpoolCfg.js)
  - BulletEffectCfg (BulletEffectCfg.js)
  - Cfg (Cfg.js)
  - CurrencyConfigCfg (CurrencyConfigCfg.js)
  - dmmItemCfg (dmmItemCfg.js)
  - dmmRoleCfg (dmmRoleCfg.js)
  - dragonPathCfg (dragonPathCfg.js)
  - DropConfigCfg (DropConfigCfg.js)
  - EquipLvCfg (EquipLvCfg.js)
  - EquipMergeLvCfg (EquipMergeLvCfg.js)
  - GameatrCfg (GameatrCfg.js)
  - GameSettingCfg (GameSettingCfg.js)
  - GuideCfg (GuideCfg.js)
  - languageCfg (languageCfg.js)
  - LevelExpCfg (LevelExpCfg.js)
  - LvInsideCfg (LvInsideCfg.js)
  - LvOutsideCfg (LvOutsideCfg.js)
  - MapCfg (MapCfg.js)
  - MiniGameEquipCfg (MiniGameEquipCfg.js)
  - MiniGameLvCfg (MiniGameLvCfg.js)
  - MonsterCfg (MonsterCfg.js)
  - MonsterLvCfg (MonsterLvCfg.js)
  - PayShopCfg (PayShopCfg.js)
  - PoolListCfg (PoolListCfg.js)
  - ProcessRewardsCfg (ProcessRewardsCfg.js)
  - randomNameCfg (randomNameCfg.js)
  - RoleCfg (RoleCfg.js)
  - RoleLvCfg (RoleLvCfg.js)
  - RoleUnlockCfg (RoleUnlockCfg.js)
  - SdkConfig (SdkConfig.js)
  - signCfg (signCfg.js)
  - SkiilpoolCfg (SkiilpoolCfg.js)
  - SkillCfg (SkillCfg.js)
  - SoundCfg (SoundCfg.js)
  - TaskCfg (TaskCfg.js)
  - TaskTypeCfg (TaskTypeCfg.js)
  - TConfig (TConfig.js)
  - TowerAmethystRewardCfg (TowerAmethystRewardCfg.js)
  - TowerCfg (TowerCfg.js)
  - TowerCoinRewardCfg (TowerCoinRewardCfg.js)
  - TowerLvCfg (TowerLvCfg.js)
  - TowerMenuCfg (TowerMenuCfg.js)
  - WeatherCfg (WeatherCfg.js)

CONTROLLER 类型:
  - ADController (ADController.js)
  - BottomBarController (BottomBarController.js)
  - BuffController (BuffController.js)
  - EventController (EventController.js)
  - FightController (FightController.js)
  - GuidesController (GuidesController.js)
  - ItemController (ItemController.js)
  - LoadingController (LoadingController.js)
  - ModeAllOutAttackController (ModeAllOutAttackController.js)
  - ModeBackpackHeroController (ModeBackpackHeroController.js)
  - ModeBulletsReboundController (ModeBulletsReboundController.js)
  - ModeChainsController (ModeChainsController.js)
  - ModeDragonWarController (ModeDragonWarController.js)
  - ModeManGuardsController (ModeManGuardsController.js)
  - ModePickUpBulletsController (ModePickUpBulletsController.js)
  - ModeThrowingKnifeController (ModeThrowingKnifeController.js)
  - PayController (PayController.js)
  - RBadgeController (RBadgeController.js)
  - SettingController (SettingController.js)
  - ShopController (ShopController.js)
  - SkillController (SkillController.js)
  - TestController (TestController.js)
  - TideDefendController (TideDefendController.js)

VALUEOBJECT 类型:
  - ADModel (ADModel.js)
  - BottomBarModel (BottomBarModel.js)
  - BuffModel (BuffModel.js)
  - BulletVo (BulletVo.js)
  - EventModel (EventModel.js)
  - FightModel (FightModel.js)
  - GuidesModel (GuidesModel.js)
  - ItemModel (ItemModel.js)
  - KnapsackVo (KnapsackVo.js)
  - LoadingModel (LoadingModel.js)
  - ModeAllOutAttackModel (ModeAllOutAttackModel.js)
  - ModeBackpackHeroModel (ModeBackpackHeroModel.js)
  - ModeBulletsReboundModel (ModeBulletsReboundModel.js)
  - ModeChainsModel (ModeChainsModel.js)
  - ModeDragonWarModel (ModeDragonWarModel.js)
  - ModeManGuardsModel (ModeManGuardsModel.js)
  - ModePickUpBulletsModel (ModePickUpBulletsModel.js)
  - ModeThrowingKnifeModel (ModeThrowingKnifeModel.js)
  - PayModel (PayModel.js)
  - PropertyVo (PropertyVo.js)
  - RBadgeModel (RBadgeModel.js)
  - RecordVo (RecordVo.js)
  - SettingModel (SettingModel.js)
  - ShopModel (ShopModel.js)
  - SkillModel (SkillModel.js)
  - SwitchVo (SwitchVo.js)
  - TaskModel (TaskModel.js)
  - TestModel (TestModel.js)
  - TideDefendModel (TideDefendModel.js)
  - UserVo (UserVo.js)

VIEW 类型:
  - BottomBarView (BottomBarView.js)
  - ExchangeCodeView (ExchangeCodeView.js)
  - FightUIView (FightUIView.js)
  - GridView (GridView.js)
  - GridViewCell (GridViewCell.js)
  - GridViewFreshWork (GridViewFreshWork.js)
  - LoadingView (LoadingView.js)
  - M20_Pop_GameRewardView (M20_Pop_GameRewardView.js)
  - M20_PrePare_MenuView (M20_PrePare_MenuView.js)
  - M33_FightBuffView (M33_FightBuffView.js)
  - M33_FightUIView (M33_FightUIView.js)
  - MoreGamesView (MoreGamesView.js)
  - SettingView (SettingView.js)
  - TestView (TestView.js)

ID 类型:
  - CallID (CallID.js)
  - ListenID (ListenID.js)
  - NotifyID (NotifyID.js)
  - StorageID (StorageID.js)
