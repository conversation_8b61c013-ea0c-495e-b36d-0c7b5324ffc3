const fs = require('fs');
const path = require('path');

class JSDefinitionAnalyzer {
    constructor() {
        this.results = {
            ccclasses: [],
            staticClasses: [],
            exportedFunctions: [],
            enums: [],
            otherDefinitions: []
        };
        this.analyzedFiles = new Set();
    }

    analyzeDirectory(dirPath) {
        const files = this.getAllJSFiles(dirPath);
        console.log(`Found ${files.length} JavaScript files to analyze...`);
        
        files.forEach(file => {
            try {
                this.analyzeFile(file);
            } catch (error) {
                console.error(`Error analyzing ${file}:`, error.message);
            }
        });
        
        return this.results;
    }

    getAllJSFiles(dirPath) {
        const files = [];
        
        function traverse(currentPath) {
            const items = fs.readdirSync(currentPath);
            
            items.forEach(item => {
                if (item.endsWith('.meta')) return; // Skip meta files
                
                const fullPath = path.join(currentPath, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    traverse(fullPath);
                } else if (item.endsWith('.js')) {
                    files.push(fullPath);
                }
            });
        }
        
        traverse(dirPath);
        return files;
    }

    analyzeFile(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const relativePath = path.relative('assets/_script', filePath);
        const fileName = path.basename(filePath, '.js');

        // Track analyzed files
        this.analyzedFiles.add(relativePath);

        // Analyze ccclass definitions
        this.findCCClasses(content, relativePath, fileName);

        // Analyze static classes
        this.findStaticClasses(content, relativePath, fileName);

        // Analyze exported functions
        this.findExportedFunctions(content, relativePath, fileName);

        // Analyze enums
        this.findEnums(content, relativePath, fileName);

        // Analyze other definitions
        this.findOtherDefinitions(content, relativePath, fileName);
    }

    findCCClasses(content, filePath, fileName) {
        // Pattern for return cc__decorate([ccp_ccclass, ...], _ctor);
        const returnDecoratePattern = /return\s+cc__decorate\s*\(\s*\[\s*ccp_ccclass[^\]]*\]\s*,\s*(\w+)\s*\)/g;

        // Pattern for compiled ccclass with cc__decorate at assignment
        const decoratePattern = /(\w+)\s*=\s*cc__decorate\s*\(\s*\[\s*ccp_ccclass/g;

        // Pattern for extends relationship
        const extendsPattern = /cc__extends\s*\(\s*(\w+)\s*,\s*(\w+)\s*\)/g;

        // Pattern for function definition that might be a component
        const componentFunctionPattern = /var\s+(def_\w+|\w+)\s*=\s*function\s*\([^)]*\)\s*{[\s\S]*?cc__extends/g;

        let match;
        let extendsMap = new Map();
        let classNames = new Set();

        // Collect extends relationships
        while ((match = extendsPattern.exec(content)) !== null) {
            extendsMap.set(match[1], match[2]);
        }

        // Find return cc__decorate pattern (most common in compiled code)
        while ((match = returnDecoratePattern.exec(content)) !== null) {
            const className = match[1];
            const extendsClass = extendsMap.get(className);

            // Try to get real class name from file name if variable name is generic
            const realClassName = (className.length === 1 || className === '_ctor') ? fileName : className;

            if (!classNames.has(realClassName)) {
                classNames.add(realClassName);
                this.results.ccclasses.push({
                    name: realClassName,
                    variableName: className,
                    extends: extendsClass || null,
                    file: filePath
                });
            }
        }

        // Find ccclass decorations at assignment
        while ((match = decoratePattern.exec(content)) !== null) {
            const className = match[1];
            const extendsClass = extendsMap.get(className);

            // Try to get real class name from file name if variable name is generic
            const realClassName = (className.length === 1) ? fileName : className;

            if (!classNames.has(realClassName)) {
                classNames.add(realClassName);
                this.results.ccclasses.push({
                    name: realClassName,
                    variableName: className,
                    extends: extendsClass || null,
                    file: filePath
                });
            }
        }

        // Find component functions that extend something
        while ((match = componentFunctionPattern.exec(content)) !== null) {
            const className = match[1];
            const extendsClass = extendsMap.get('_ctor') || extendsMap.get(className);

            // If it has cc__extends, it's likely a component
            if (extendsClass) {
                const realClassName = className.startsWith('def_') ? className.substring(4) :
                                    (className.length === 1 ? fileName : className);

                if (!classNames.has(realClassName)) {
                    classNames.add(realClassName);
                    this.results.ccclasses.push({
                        name: realClassName,
                        variableName: className,
                        extends: extendsClass,
                        file: filePath
                    });
                }
            }
        }
    }

    findStaticClasses(content, filePath, fileName) {
        const found = new Set();
        
        // Manager classes
        if (fileName.includes('Manager') || fileName.includes('Util') || fileName.includes('Helper')) {
            found.add(fileName);
            this.results.staticClasses.push({
                name: fileName,
                type: 'manager',
                file: filePath
            });
        }
        
        // Look for singleton pattern
        if (content.includes('getInstance') && content.includes('function')) {
            if (!found.has(fileName)) {
                found.add(fileName);
                this.results.staticClasses.push({
                    name: fileName,
                    type: 'singleton',
                    file: filePath
                });
            }
        }
        
        // Look for static utility functions on window
        const windowPattern = /window\.(\w+)\s*=\s*{/g;
        let match;
        while ((match = windowPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.staticClasses.push({
                    name: match[1],
                    type: 'utility',
                    file: filePath
                });
            }
        }
    }

    findExportedFunctions(content, filePath, fileName) {
        const patterns = [
            /exports\.(\w+)\s*=\s*function/g,
            /window\.(\w+)\s*=\s*function/g
        ];
        
        const found = new Set();
        
        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                if (!found.has(match[1])) {
                    found.add(match[1]);
                    this.results.exportedFunctions.push({
                        name: match[1],
                        file: filePath
                    });
                }
            }
        });
    }

    findEnums(content, filePath, fileName) {
        // Pattern for TypeScript-style enum compilation
        const enumPattern = /\(function\s*\(\s*\w+\s*\)\s*{[\s\S]*?}\)\s*\(\s*\w+\s*=\s*exports\.(\w+)\s*\|\|/g;
        
        const found = new Set();
        let match;
        
        while ((match = enumPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.enums.push({
                    name: match[1],
                    file: filePath
                });
            }
        }
    }

    findOtherDefinitions(content, filePath, fileName) {
        const found = new Set();

        // Namespace definitions (like exports.Game = ...)
        const namespacePattern = /\)\s*\(\s*exports\.(\w+)\s*\|\|/g;
        let match;
        while ((match = namespacePattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.otherDefinitions.push({
                    name: match[1],
                    type: 'namespace',
                    file: filePath
                });
            }
        }

        // Exported class definitions (exports.ClassName = ...)
        const exportedClassPattern = /exports\.(\w+)\s*=\s*exp_(\w+)/g;
        while ((match = exportedClassPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.otherDefinitions.push({
                    name: match[1],
                    type: 'exportedClass',
                    file: filePath
                });
            }
        }

        // Configuration files
        if (fileName.endsWith('Cfg') || fileName.includes('Config')) {
            if (!found.has(fileName)) {
                found.add(fileName);
                this.results.otherDefinitions.push({
                    name: fileName,
                    type: 'config',
                    file: filePath
                });
            }
        }

        // Value objects
        if (fileName.endsWith('Vo') || fileName.includes('Model') || fileName.includes('Data')) {
            if (!found.has(fileName)) {
                found.add(fileName);
                this.results.otherDefinitions.push({
                    name: fileName,
                    type: 'valueObject',
                    file: filePath
                });
            }
        }

        // ID definitions
        if (fileName.endsWith('ID') || fileName.endsWith('Id')) {
            if (!found.has(fileName)) {
                found.add(fileName);
                this.results.otherDefinitions.push({
                    name: fileName,
                    type: 'id',
                    file: filePath
                });
            }
        }

        // Controllers
        if (fileName.includes('Controller')) {
            if (!found.has(fileName)) {
                found.add(fileName);
                this.results.otherDefinitions.push({
                    name: fileName,
                    type: 'controller',
                    file: filePath
                });
            }
        }

        // Views
        if (fileName.includes('View')) {
            if (!found.has(fileName)) {
                found.add(fileName);
                this.results.otherDefinitions.push({
                    name: fileName,
                    type: 'view',
                    file: filePath
                });
            }
        }

        // Utility classes (if not already categorized)
        if ((fileName.includes('Util') || fileName.includes('Helper') || fileName.includes('Tool')) && !found.has(fileName)) {
            found.add(fileName);
            this.results.otherDefinitions.push({
                name: fileName,
                type: 'utility',
                file: filePath
            });
        }
    }

    generateReport() {
        const report = {
            summary: {
                totalFiles: this.analyzedFiles.size,
                filesWithDefinitions: new Set([
                    ...this.results.ccclasses.map(c => c.file),
                    ...this.results.staticClasses.map(c => c.file),
                    ...this.results.exportedFunctions.map(f => f.file),
                    ...this.results.enums.map(e => e.file),
                    ...this.results.otherDefinitions.map(d => d.file)
                ]).size,
                ccclassCount: this.results.ccclasses.length,
                staticClassCount: this.results.staticClasses.length,
                exportedFunctionCount: this.results.exportedFunctions.length,
                enumCount: this.results.enums.length,
                otherDefinitionCount: this.results.otherDefinitions.length
            },
            details: this.results
        };

        return report;
    }
}

// Main execution
const analyzer = new JSDefinitionAnalyzer();
const results = analyzer.analyzeDirectory('assets/_script');
const report = analyzer.generateReport();

// Write report to JSON file
fs.writeFileSync('js_definitions_report.json', JSON.stringify(report, null, 2));

console.log('\n=== JavaScript Definition Analysis Report ===');
console.log(`Total files analyzed: ${report.summary.totalFiles}`);
console.log(`CCClass definitions: ${report.summary.ccclassCount}`);
console.log(`Static classes: ${report.summary.staticClassCount}`);
console.log(`Exported functions: ${report.summary.exportedFunctionCount}`);
console.log(`Enums: ${report.summary.enumCount}`);
console.log(`Other definitions: ${report.summary.otherDefinitionCount}`);
console.log('\nDetailed report saved to: js_definitions_report.json');
