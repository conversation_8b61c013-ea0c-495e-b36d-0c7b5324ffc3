const fs = require('fs');
const path = require('path');

class JSDefinitionAnalyzer {
    constructor() {
        this.results = {
            ccclasses: [],
            staticClasses: [],
            exportedFunctions: [],
            enums: [],
            otherDefinitions: []
        };
    }

    analyzeDirectory(dirPath) {
        const files = this.getAllJSFiles(dirPath);
        console.log(`Found ${files.length} JavaScript files to analyze...`);
        
        files.forEach(file => {
            try {
                this.analyzeFile(file);
            } catch (error) {
                console.error(`Error analyzing ${file}:`, error.message);
            }
        });
        
        return this.results;
    }

    getAllJSFiles(dirPath) {
        const files = [];
        
        function traverse(currentPath) {
            const items = fs.readdirSync(currentPath);
            
            items.forEach(item => {
                if (item.endsWith('.meta')) return; // Skip meta files
                
                const fullPath = path.join(currentPath, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    traverse(fullPath);
                } else if (item.endsWith('.js')) {
                    files.push(fullPath);
                }
            });
        }
        
        traverse(dirPath);
        return files;
    }

    analyzeFile(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const relativePath = path.relative('assets/_script', filePath);
        const fileName = path.basename(filePath, '.js');
        
        // Analyze ccclass definitions
        this.findCCClasses(content, relativePath, fileName);
        
        // Analyze static classes
        this.findStaticClasses(content, relativePath, fileName);
        
        // Analyze exported functions
        this.findExportedFunctions(content, relativePath, fileName);
        
        // Analyze enums
        this.findEnums(content, relativePath, fileName);
        
        // Analyze other definitions
        this.findOtherDefinitions(content, relativePath, fileName);
    }

    findCCClasses(content, filePath, fileName) {
        // Pattern for compiled ccclass with cc__decorate
        const decoratePattern = /(\w+)\s*=\s*cc__decorate\s*\(\s*\[\s*ccp_ccclass/g;
        
        // Pattern for extends relationship
        const extendsPattern = /cc__extends\s*\(\s*(\w+)\s*,\s*(\w+)\s*\)/g;
        
        // Pattern for function definition that might be a component
        const componentFunctionPattern = /var\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*{[\s\S]*?cc__extends/g;
        
        let match;
        let extendsMap = new Map();
        let classNames = new Set();
        
        // Collect extends relationships
        while ((match = extendsPattern.exec(content)) !== null) {
            extendsMap.set(match[1], match[2]);
        }
        
        // Find ccclass decorations
        while ((match = decoratePattern.exec(content)) !== null) {
            const className = match[1];
            const extendsClass = extendsMap.get(className);
            
            // Try to get real class name from file name if variable name is generic
            const realClassName = (className.length === 1) ? fileName : className;
            
            if (!classNames.has(realClassName)) {
                classNames.add(realClassName);
                this.results.ccclasses.push({
                    name: realClassName,
                    variableName: className,
                    extends: extendsClass || null,
                    file: filePath
                });
            }
        }
        
        // Find component functions that extend cc.Component
        while ((match = componentFunctionPattern.exec(content)) !== null) {
            const className = match[1];
            const extendsClass = extendsMap.get(className);
            
            // Check if it extends cc.Component or similar
            if (extendsClass && (extendsClass.includes('Component') || extendsClass === 'e')) {
                const realClassName = (className.length === 1) ? fileName : className;
                
                if (!classNames.has(realClassName)) {
                    classNames.add(realClassName);
                    this.results.ccclasses.push({
                        name: realClassName,
                        variableName: className,
                        extends: extendsClass,
                        file: filePath
                    });
                }
            }
        }
    }

    findStaticClasses(content, filePath, fileName) {
        const found = new Set();
        
        // Manager classes
        if (fileName.includes('Manager') || fileName.includes('Util') || fileName.includes('Helper')) {
            found.add(fileName);
            this.results.staticClasses.push({
                name: fileName,
                type: 'manager',
                file: filePath
            });
        }
        
        // Look for singleton pattern
        if (content.includes('getInstance') && content.includes('function')) {
            if (!found.has(fileName)) {
                found.add(fileName);
                this.results.staticClasses.push({
                    name: fileName,
                    type: 'singleton',
                    file: filePath
                });
            }
        }
        
        // Look for static utility functions on window
        const windowPattern = /window\.(\w+)\s*=\s*{/g;
        let match;
        while ((match = windowPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.staticClasses.push({
                    name: match[1],
                    type: 'utility',
                    file: filePath
                });
            }
        }
    }

    findExportedFunctions(content, filePath, fileName) {
        const patterns = [
            /exports\.(\w+)\s*=\s*function/g,
            /window\.(\w+)\s*=\s*function/g
        ];
        
        const found = new Set();
        
        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                if (!found.has(match[1])) {
                    found.add(match[1]);
                    this.results.exportedFunctions.push({
                        name: match[1],
                        file: filePath
                    });
                }
            }
        });
    }

    findEnums(content, filePath, fileName) {
        // Pattern for TypeScript-style enum compilation
        const enumPattern = /\(function\s*\(\s*\w+\s*\)\s*{[\s\S]*?}\)\s*\(\s*\w+\s*=\s*exports\.(\w+)\s*\|\|/g;
        
        const found = new Set();
        let match;
        
        while ((match = enumPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.enums.push({
                    name: match[1],
                    file: filePath
                });
            }
        }
    }

    findOtherDefinitions(content, filePath, fileName) {
        const found = new Set();
        
        // Configuration files
        if (fileName.endsWith('Cfg') || fileName.includes('Config')) {
            found.add(fileName);
            this.results.otherDefinitions.push({
                name: fileName,
                type: 'config',
                file: filePath
            });
        }
        
        // Value objects
        if (fileName.endsWith('Vo') || fileName.includes('Model') || fileName.includes('Data')) {
            if (!found.has(fileName)) {
                found.add(fileName);
                this.results.otherDefinitions.push({
                    name: fileName,
                    type: 'valueObject',
                    file: filePath
                });
            }
        }
        
        // ID definitions
        if (fileName.endsWith('ID') || fileName.endsWith('Id')) {
            if (!found.has(fileName)) {
                found.add(fileName);
                this.results.otherDefinitions.push({
                    name: fileName,
                    type: 'id',
                    file: filePath
                });
            }
        }
        
        // Controllers
        if (fileName.includes('Controller')) {
            if (!found.has(fileName)) {
                found.add(fileName);
                this.results.otherDefinitions.push({
                    name: fileName,
                    type: 'controller',
                    file: filePath
                });
            }
        }
        
        // Views
        if (fileName.includes('View')) {
            if (!found.has(fileName)) {
                found.add(fileName);
                this.results.otherDefinitions.push({
                    name: fileName,
                    type: 'view',
                    file: filePath
                });
            }
        }
    }

    generateReport() {
        const report = {
            summary: {
                totalFiles: new Set([
                    ...this.results.ccclasses.map(c => c.file),
                    ...this.results.staticClasses.map(c => c.file),
                    ...this.results.exportedFunctions.map(f => f.file),
                    ...this.results.enums.map(e => e.file),
                    ...this.results.otherDefinitions.map(d => d.file)
                ]).size,
                ccclassCount: this.results.ccclasses.length,
                staticClassCount: this.results.staticClasses.length,
                exportedFunctionCount: this.results.exportedFunctions.length,
                enumCount: this.results.enums.length,
                otherDefinitionCount: this.results.otherDefinitions.length
            },
            details: this.results
        };
        
        return report;
    }
}

// Main execution
const analyzer = new JSDefinitionAnalyzer();
const results = analyzer.analyzeDirectory('assets/_script');
const report = analyzer.generateReport();

// Write report to JSON file
fs.writeFileSync('js_definitions_report.json', JSON.stringify(report, null, 2));

console.log('\n=== JavaScript Definition Analysis Report ===');
console.log(`Total files analyzed: ${report.summary.totalFiles}`);
console.log(`CCClass definitions: ${report.summary.ccclassCount}`);
console.log(`Static classes: ${report.summary.staticClassCount}`);
console.log(`Exported functions: ${report.summary.exportedFunctionCount}`);
console.log(`Enums: ${report.summary.enumCount}`);
console.log(`Other definitions: ${report.summary.otherDefinitionCount}`);
console.log('\nDetailed report saved to: js_definitions_report.json');
