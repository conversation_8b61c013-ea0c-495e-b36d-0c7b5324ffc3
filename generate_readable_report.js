const fs = require('fs');

// Read the JSON report
const report = JSON.parse(fs.readFileSync('js_definitions_report.json', 'utf8'));

let output = '';

output += '='.repeat(80) + '\n';
output += '                    JavaScript 脚本定义分析报告\n';
output += '='.repeat(80) + '\n\n';

output += `分析概要:\n`;
output += `- 总文件数: ${report.summary.totalFiles}\n`;
output += `- 包含定义的文件数: ${report.summary.filesWithDefinitions || 'N/A'}\n`;
output += `- CCClass 组件类: ${report.summary.ccclassCount}\n`;
output += `- 静态类/管理器: ${report.summary.staticClassCount}\n`;
output += `- 导出函数: ${report.summary.exportedFunctionCount}\n`;
output += `- 枚举类型: ${report.summary.enumCount}\n`;
output += `- 其他定义: ${report.summary.otherDefinitionCount}\n`;
output += `- 未识别文件数: ${report.summary.totalFiles - report.summary.filesWithDefinitions}\n\n`;

// CCClass 组件类
output += '1. CCClass 组件类 (继承关系)\n';
output += '-'.repeat(50) + '\n';
if (report.details.ccclasses.length > 0) {
    report.details.ccclasses.forEach(cls => {
        output += `- ${cls.name}`;
        if (cls.extends) {
            output += ` extends ${cls.extends}`;
        }
        output += ` (${cls.file})\n`;
    });
} else {
    output += '未找到明确的 CCClass 定义\n';
}
output += '\n';

// 静态类/管理器
output += '2. 静态类/管理器类\n';
output += '-'.repeat(50) + '\n';
const managerTypes = {};
report.details.staticClasses.forEach(cls => {
    if (!managerTypes[cls.type]) {
        managerTypes[cls.type] = [];
    }
    managerTypes[cls.type].push(cls);
});

Object.keys(managerTypes).forEach(type => {
    output += `\n${type.toUpperCase()} 类型:\n`;
    managerTypes[type].forEach(cls => {
        output += `  - ${cls.name} (${cls.file})\n`;
    });
});
output += '\n';

// 导出函数
output += '3. 直接导出的函数\n';
output += '-'.repeat(50) + '\n';
const functionsByFile = {};
report.details.exportedFunctions.forEach(func => {
    if (!functionsByFile[func.file]) {
        functionsByFile[func.file] = [];
    }
    functionsByFile[func.file].push(func.name);
});

Object.keys(functionsByFile).forEach(file => {
    output += `${file}:\n`;
    functionsByFile[file].forEach(funcName => {
        output += `  - ${funcName}()\n`;
    });
});
output += '\n';

// 枚举类型
output += '4. 枚举类型\n';
output += '-'.repeat(50) + '\n';
const enumsByFile = {};
report.details.enums.forEach(enumDef => {
    if (!enumsByFile[enumDef.file]) {
        enumsByFile[enumDef.file] = [];
    }
    enumsByFile[enumDef.file].push(enumDef.name);
});

Object.keys(enumsByFile).forEach(file => {
    output += `${file}:\n`;
    enumsByFile[file].forEach(enumName => {
        output += `  - ${enumName}\n`;
    });
});
output += '\n';

// 其他定义
output += '5. 其他定义\n';
output += '-'.repeat(50) + '\n';
const otherTypes = {};
report.details.otherDefinitions.forEach(def => {
    if (!otherTypes[def.type]) {
        otherTypes[def.type] = [];
    }
    otherTypes[def.type].push(def);
});

// Sort types by importance
const typeOrder = ['namespace', 'exportedClass', 'config', 'controller', 'valueObject', 'view', 'id', 'utility'];
const sortedTypes = typeOrder.filter(type => otherTypes[type]);

sortedTypes.forEach(type => {
    output += `\n${type.toUpperCase()} 类型 (${otherTypes[type].length}个):\n`;
    otherTypes[type].slice(0, 20).forEach(def => {  // Show first 20 items
        output += `  - ${def.name} (${def.file})\n`;
    });
    if (otherTypes[type].length > 20) {
        output += `  ... 还有 ${otherTypes[type].length - 20} 个\n`;
    }
});

// 写入文件
fs.writeFileSync('js_definitions_report.txt', output);
console.log('可读性报告已生成: js_definitions_report.txt');
