{"summary": {"totalFiles": 70, "ccclassCount": 9, "staticClassCount": 33, "exportedFunctionCount": 25, "enumCount": 15, "otherDefinitionCount": 131}, "details": {"ccclasses": [{"name": "o", "extends": null, "file": "BaseEntity.js"}, {"name": "o", "extends": null, "file": "Commonguide.js"}, {"name": "o", "extends": null, "file": "EnergyStamp.js"}, {"name": "o", "extends": null, "file": "FCollider.js"}, {"name": "o", "extends": null, "file": "GameSkeleton.js"}, {"name": "o", "extends": null, "file": "GoodsUIItem.js"}, {"name": "o", "extends": null, "file": "M20Prop_Equip.js"}, {"name": "o", "extends": null, "file": "M20Prop_Gemstone.js"}, {"name": "o", "extends": null, "file": "MCBoss.js"}], "staticClasses": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "AlertManager.js"}, {"name": "exp_<PERSON>ert<PERSON>ger", "file": "AlertManager.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "AlertManager.js"}, {"name": "AudioManager", "file": "AudioManager.js"}, {"name": "exp_AudioManager", "file": "AudioManager.js"}, {"name": "AudioManager", "file": "AudioManager.js"}, {"name": "def_CompManager", "file": "CompManager.js"}, {"name": "def_FColliderManager", "file": "FColliderManager.js"}, {"name": "GameUtil", "file": "GameUtil.js"}, {"name": "exp_GameUtil", "file": "GameUtil.js"}, {"name": "GameUtil", "file": "GameUtil.js"}, {"name": "Manager", "file": "Manager.js"}, {"name": "Manager", "file": "Manager.js"}, {"name": "NetManager", "file": "NetManager.js"}, {"name": "exp_NetManager", "file": "NetManager.js"}, {"name": "NetManager", "file": "NetManager.js"}, {"name": "Listener<PERSON><PERSON><PERSON>", "file": "NotifyListener.js"}, {"name": "exp_ListenerManager", "file": "NotifyListener.js"}, {"name": "Listener<PERSON><PERSON><PERSON>", "file": "NotifyListener.js"}, {"name": "PoolManager", "file": "Pool.js"}, {"name": "PoolManager", "file": "Pool.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "file": "ResUtil.js"}, {"name": "exp_ResUtil", "file": "ResUtil.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "file": "ResUtil.js"}, {"name": "StorageManager", "file": "StorageManager.js"}, {"name": "exp_StorageManager", "file": "StorageManager.js"}, {"name": "StorageManager", "file": "StorageManager.js"}, {"name": "UIManager", "file": "UIManager.js"}, {"name": "exp_UIManager", "file": "UIManager.js"}, {"name": "UIManager", "file": "UIManager.js"}, {"name": "VoManager", "file": "VoManager.js"}, {"name": "exp_VoManager", "file": "VoManager.js"}, {"name": "VoManager", "file": "VoManager.js"}], "exportedFunctions": [{"name": "click", "file": "Api.js"}, {"name": "report", "file": "Api.js"}, {"name": "getOpenid", "file": "Api.js"}, {"name": "serverTime", "file": "Api.js"}, {"name": "initlang", "file": "cc_language.js"}, {"name": "versionFormat", "file": "function.js"}, {"name": "formatDate", "file": "function.js"}, {"name": "response", "file": "function.js"}, {"name": "getSign", "file": "function.js"}, {"name": "getServerTime", "file": "function.js"}, {"name": "cacheFunction", "file": "GameUtil.js"}, {"name": "getClassByName", "file": "GameUtil.js"}, {"name": "isNullOrEmpty", "file": "Global.js"}, {"name": "copy", "file": "Global.js"}, {"name": "toArray", "file": "Global.js"}, {"name": "GameDeepCopy", "file": "Global.js"}, {"name": "iOSSendMsg", "file": "IOSSdk.js"}, {"name": "iOSBuySendMsg", "file": "IOSSdk.js"}, {"name": "md5", "file": "md51.js"}, {"name": "ActivityPass", "file": "ModeBackpackHeroModel.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>au<PERSON><PERSON>", "file": "ModuleLauncher.js"}, {"name": "onpagehide", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "onbeforeunload", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "__dynamicPropertiesForNative", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "UILauncher", "file": "UILauncher.js"}], "enums": [{"name": "AlertType", "file": "AlertManager.js"}, {"name": "ItemAlertType", "file": "AlertManager.js"}, {"name": "AudioType", "file": "AudioManager.js"}, {"name": "PlayType", "file": "AudioManager.js"}, {"name": "CampType", "file": "BaseEntity.js"}, {"name": "EntityType", "file": "BaseEntity.js"}, {"name": "ColliderType", "file": "FCollider.js"}, {"name": "GRID_TYPE", "file": "GridView.js"}, {"name": "MoreGames", "file": "MoreGamesView.js"}, {"name": "NodeState", "file": "NodePool.js"}, {"name": "Hurt", "file": "PropertyVo.js"}, {"name": "RBadge", "file": "RBadgeModel.js"}, {"name": "TaskSaveType", "file": "TaskModel.js"}, {"name": "LAYOUT_HORIZONTAL_TYPE", "file": "TwoDLayoutObject.js"}, {"name": "LAYOUT_VERTICAL_TYPE", "file": "TwoDLayoutObject.js"}], "otherDefinitions": [{"name": "_", "type": "constant", "file": "BottomBarView.js"}, {"name": "_", "type": "constant", "file": "BuffList.js"}, {"name": "BulletVo", "type": "valueObject", "file": "BulletVo.js"}, {"name": "exp_BulletVo", "type": "valueObject", "file": "BulletVo.js"}, {"name": "BulletVo", "type": "valueObject", "file": "BulletVo.js"}, {"name": "RedPointID", "type": "id", "file": "CallID.js"}, {"name": "CallID", "type": "id", "file": "CallID.js"}, {"name": "CallID", "type": "id", "file": "CallID.js"}, {"name": "RedPointID", "type": "id", "file": "CallID.js"}, {"name": "_", "type": "constant", "file": "cc_language.js"}, {"name": "M", "type": "constant", "file": "cc_language.js"}, {"name": "C", "type": "constant", "file": "cc_language.js"}, {"name": "S", "type": "constant", "file": "cc_language.js"}, {"name": "P", "type": "constant", "file": "cc_language.js"}, {"name": "Cfg", "type": "config", "file": "Cfg.js"}, {"name": "Cfg", "type": "config", "file": "Cfg.js"}, {"name": "M", "type": "constant", "file": "Dragon.js"}, {"name": "S", "type": "constant", "file": "DragonBody.js"}, {"name": "_", "type": "constant", "file": "FColliderManager.js"}, {"name": "M", "type": "constant", "file": "FColliderManager.js"}, {"name": "_", "type": "constant", "file": "FColliderManager.js"}, {"name": "M", "type": "constant", "file": "FColliderManager.js"}, {"name": "C", "type": "constant", "file": "FColliderManager.js"}, {"name": "S", "type": "constant", "file": "FColliderManager.js"}, {"name": "P", "type": "constant", "file": "FColliderManager.js"}, {"name": "ModeCfg", "type": "config", "file": "Game.js"}, {"name": "exp_ModeCfg", "type": "config", "file": "Game.js"}, {"name": "ModeCfg", "type": "config", "file": "Game.js"}, {"name": "T", "type": "constant", "file": "Game.js"}, {"name": "A", "type": "constant", "file": "Game.js"}, {"name": "R", "type": "constant", "file": "Game.js"}, {"name": "B", "type": "constant", "file": "Game.js"}, {"name": "L", "type": "constant", "file": "Game.js"}, {"name": "C", "type": "constant", "file": "Goods.js"}, {"name": "_", "type": "constant", "file": "GTAssembler2D.js"}, {"name": "M", "type": "constant", "file": "GTAssembler2D.js"}, {"name": "C", "type": "constant", "file": "GTAssembler2D.js"}, {"name": "S", "type": "constant", "file": "GTAssembler2D.js"}, {"name": "_", "type": "constant", "file": "GTSimpleSpriteAssembler2D.js"}, {"name": "M", "type": "constant", "file": "GTSimpleSpriteAssembler2D.js"}, {"name": "KnapsackVo", "type": "valueObject", "file": "KnapsackVo.js"}, {"name": "KnapsackVo", "type": "valueObject", "file": "KnapsackVo.js"}, {"name": "ListenID", "type": "id", "file": "ListenID.js"}, {"name": "ListenID", "type": "id", "file": "ListenID.js"}, {"name": "M", "type": "constant", "file": "M20_PrePare_Fight.js"}, {"name": "S", "type": "constant", "file": "M33_FightUIView.js"}, {"name": "T", "type": "constant", "file": "MBackpackHero.js"}, {"name": "A", "type": "constant", "file": "MBackpackHero.js"}, {"name": "R", "type": "constant", "file": "MBackpackHero.js"}, {"name": "S", "type": "constant", "file": "MBRebound.js"}, {"name": "P", "type": "constant", "file": "MBRebound.js"}, {"name": "A", "type": "constant", "file": "MChains.js"}, {"name": "R", "type": "constant", "file": "MChains.js"}, {"name": "B", "type": "constant", "file": "MChains.js"}, {"name": "_", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "M", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "C", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "S", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "P", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "O", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "I", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "D", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "T", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "A", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "R", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "B", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "L", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "N", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "E", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "V", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "G", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "F", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "U", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "H", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "W", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "K", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "J", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "Y", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "Z", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "Q", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "_", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "M", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "C", "type": "constant", "file": "ModeBackpackHeroModel.js"}, {"name": "_", "type": "constant", "file": "ModeChainsModel.js"}, {"name": "M", "type": "constant", "file": "ModeChainsModel.js"}, {"name": "_", "type": "constant", "file": "Monster.js"}, {"name": "C", "type": "constant", "file": "MTideDefendRebound.js"}, {"name": "S", "type": "constant", "file": "MTideDefendRebound.js"}, {"name": "M", "type": "constant", "file": "MTKnife.js"}, {"name": "_", "type": "constant", "file": "MVC.js"}, {"name": "M", "type": "constant", "file": "MVC.js"}, {"name": "C", "type": "constant", "file": "MVC.js"}, {"name": "NotifyID", "type": "id", "file": "NotifyID.js"}, {"name": "NotifyID", "type": "id", "file": "NotifyID.js"}, {"name": "RecordVo", "type": "valueObject", "file": "RecordVo.js"}, {"name": "RecordVo", "type": "valueObject", "file": "RecordVo.js"}, {"name": "C", "type": "constant", "file": "RoleSkillList.js"}, {"name": "TaSdkID", "type": "id", "file": "SdkConfig.js"}, {"name": "TaSdkID", "type": "id", "file": "SdkConfig.js"}, {"name": "StorageID", "type": "id", "file": "StorageID.js"}, {"name": "StorageID", "type": "id", "file": "StorageID.js"}, {"name": "SwitchVo", "type": "valueObject", "file": "SwitchVo.js"}, {"name": "exp_SwitchVo", "type": "valueObject", "file": "SwitchVo.js"}, {"name": "SwitchVo", "type": "valueObject", "file": "SwitchVo.js"}, {"name": "_", "type": "constant", "file": "TestView.js"}, {"name": "C", "type": "constant", "file": "TestView.js"}, {"name": "S", "type": "constant", "file": "TestView.js"}, {"name": "P", "type": "constant", "file": "TestView.js"}, {"name": "O", "type": "constant", "file": "TestView.js"}, {"name": "I", "type": "constant", "file": "TestView.js"}, {"name": "_", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "M", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "C", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "S", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "P", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "O", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "I", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "D", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "T", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "A", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "R", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "B", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "L", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "N", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "E", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "V", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "G", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "F", "type": "constant", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "UserVo", "type": "valueObject", "file": "UserVo.js"}, {"name": "exp_UserVo", "type": "valueObject", "file": "UserVo.js"}, {"name": "UserVo", "type": "valueObject", "file": "UserVo.js"}]}}