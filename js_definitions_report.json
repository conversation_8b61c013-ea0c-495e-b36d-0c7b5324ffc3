{"summary": {"totalFiles": 169, "ccclassCount": 18, "staticClassCount": 23, "exportedFunctionCount": 25, "enumCount": 15, "otherDefinitionCount": 125}, "details": {"ccclasses": [{"name": "BaseEntity", "variableName": "o", "extends": null, "file": "BaseEntity.js"}, {"name": "Commonguide", "variableName": "o", "extends": null, "file": "Commonguide.js"}, {"name": "EnergyStamp", "variableName": "o", "extends": null, "file": "EnergyStamp.js"}, {"name": "FCollider", "variableName": "o", "extends": null, "file": "FCollider.js"}, {"name": "GameSkeleton", "variableName": "o", "extends": null, "file": "GameSkeleton.js"}, {"name": "GoodsUIItem", "variableName": "o", "extends": null, "file": "GoodsUIItem.js"}, {"name": "LevelMgr", "variableName": "t", "extends": "e", "file": "LevelMgr.js"}, {"name": "M20Prop_Equip", "variableName": "o", "extends": null, "file": "M20Prop_Equip.js"}, {"name": "M20Prop_Gemstone", "variableName": "o", "extends": null, "file": "M20Prop_Gemstone.js"}, {"name": "MCBoss", "variableName": "o", "extends": null, "file": "MCBoss.js"}, {"name": "MCBossState", "variableName": "t", "extends": "e", "file": "MCBossState.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "variableName": "o", "extends": "e", "file": "MMGuards.js"}, {"name": "MonsterState", "variableName": "t", "extends": "e", "file": "MonsterState.js"}, {"name": "MonsterTidalState", "variableName": "t", "extends": "e", "file": "MonsterTidalState.js"}, {"name": "MVC", "variableName": "t", "extends": "e", "file": "MVC.js"}, {"name": "PetState", "variableName": "t", "extends": "e", "file": "PetState.js"}, {"name": "PropertyVo", "variableName": "o", "extends": "e", "file": "PropertyVo.js"}, {"name": "RoleState", "variableName": "t", "extends": "e", "file": "RoleState.js"}], "staticClasses": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "manager", "file": "AlertManager.js"}, {"name": "AudioManager", "type": "manager", "file": "AudioManager.js"}, {"name": "BronMonsterManger", "type": "singleton", "file": "BronMonsterManger.js"}, {"name": "CompManager", "type": "manager", "file": "CompManager.js"}, {"name": "FColliderManager", "type": "manager", "file": "FColliderManager.js"}, {"name": "FightController", "type": "singleton", "file": "FightController.js"}, {"name": "FightModel", "type": "singleton", "file": "FightModel.js"}, {"name": "GameUtil", "type": "manager", "file": "GameUtil.js"}, {"name": "Manager", "type": "manager", "file": "Manager.js"}, {"name": "MathUtils", "type": "manager", "file": "MathUtils.js"}, {"name": "MVC", "type": "singleton", "file": "MVC.js"}, {"name": "NetManager", "type": "manager", "file": "NetManager.js"}, {"name": "NPC", "type": "singleton", "file": "NPC.js"}, {"name": "PayController", "type": "singleton", "file": "PayController.js"}, {"name": "PayModel", "type": "singleton", "file": "PayModel.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "manager", "file": "ResUtil.js"}, {"name": "Role", "type": "singleton", "file": "Role.js"}, {"name": "SkillController", "type": "singleton", "file": "SkillController.js"}, {"name": "SkillManager", "type": "manager", "file": "SkillManager.js"}, {"name": "SkillModel", "type": "singleton", "file": "SkillModel.js"}, {"name": "StorageManager", "type": "manager", "file": "StorageManager.js"}, {"name": "UIManager", "type": "manager", "file": "UIManager.js"}, {"name": "VoManager", "type": "manager", "file": "VoManager.js"}], "exportedFunctions": [{"name": "click", "file": "Api.js"}, {"name": "report", "file": "Api.js"}, {"name": "getOpenid", "file": "Api.js"}, {"name": "serverTime", "file": "Api.js"}, {"name": "initlang", "file": "cc_language.js"}, {"name": "versionFormat", "file": "function.js"}, {"name": "formatDate", "file": "function.js"}, {"name": "response", "file": "function.js"}, {"name": "getSign", "file": "function.js"}, {"name": "getServerTime", "file": "function.js"}, {"name": "cacheFunction", "file": "GameUtil.js"}, {"name": "getClassByName", "file": "GameUtil.js"}, {"name": "isNullOrEmpty", "file": "Global.js"}, {"name": "copy", "file": "Global.js"}, {"name": "toArray", "file": "Global.js"}, {"name": "GameDeepCopy", "file": "Global.js"}, {"name": "iOSSendMsg", "file": "IOSSdk.js"}, {"name": "iOSBuySendMsg", "file": "IOSSdk.js"}, {"name": "md5", "file": "md51.js"}, {"name": "ActivityPass", "file": "ModeBackpackHeroModel.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>au<PERSON><PERSON>", "file": "ModuleLauncher.js"}, {"name": "onpagehide", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "onbeforeunload", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "__dynamicPropertiesForNative", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "UILauncher", "file": "UILauncher.js"}], "enums": [{"name": "AlertType", "file": "AlertManager.js"}, {"name": "ItemAlertType", "file": "AlertManager.js"}, {"name": "AudioType", "file": "AudioManager.js"}, {"name": "PlayType", "file": "AudioManager.js"}, {"name": "CampType", "file": "BaseEntity.js"}, {"name": "EntityType", "file": "BaseEntity.js"}, {"name": "ColliderType", "file": "FCollider.js"}, {"name": "GRID_TYPE", "file": "GridView.js"}, {"name": "MoreGames", "file": "MoreGamesView.js"}, {"name": "NodeState", "file": "NodePool.js"}, {"name": "Hurt", "file": "PropertyVo.js"}, {"name": "RBadge", "file": "RBadgeModel.js"}, {"name": "TaskSaveType", "file": "TaskModel.js"}, {"name": "LAYOUT_HORIZONTAL_TYPE", "file": "TwoDLayoutObject.js"}, {"name": "LAYOUT_VERTICAL_TYPE", "file": "TwoDLayoutObject.js"}], "otherDefinitions": [{"name": "activityCfg", "type": "config", "file": "activityCfg.js"}, {"name": "ADController", "type": "controller", "file": "ADController.js"}, {"name": "ADModel", "type": "valueObject", "file": "ADModel.js"}, {"name": "adRewardCfg", "type": "config", "file": "adRewardCfg.js"}, {"name": "BagBuffCfg", "type": "config", "file": "BagBuffCfg.js"}, {"name": "BagGuideCfg", "type": "config", "file": "BagGuideCfg.js"}, {"name": "BagModeLvCfg", "type": "config", "file": "BagModeLvCfg.js"}, {"name": "BagModeSkillPoolCfg", "type": "config", "file": "BagModeSkillPoolCfg.js"}, {"name": "bagMonsterLvCfg", "type": "config", "file": "bagMonsterLvCfg.js"}, {"name": "BagShopItemCfg", "type": "config", "file": "BagShopItemCfg.js"}, {"name": "BagSkillCfg", "type": "config", "file": "BagSkillCfg.js"}, {"name": "BottomBarController", "type": "controller", "file": "BottomBarController.js"}, {"name": "BottomBarModel", "type": "valueObject", "file": "BottomBarModel.js"}, {"name": "BottomBarView", "type": "view", "file": "BottomBarView.js"}, {"name": "BoxLevelExpCfg", "type": "config", "file": "BoxLevelExpCfg.js"}, {"name": "BuffCfg", "type": "config", "file": "BuffCfg.js"}, {"name": "BuffController", "type": "controller", "file": "BuffController.js"}, {"name": "BuffModel", "type": "valueObject", "file": "BuffModel.js"}, {"name": "BuildModeSkiilpoolCfg", "type": "config", "file": "BuildModeSkiilpoolCfg.js"}, {"name": "BulletEffectCfg", "type": "config", "file": "BulletEffectCfg.js"}, {"name": "BulletVo", "type": "valueObject", "file": "BulletVo.js"}, {"name": "CallID", "type": "id", "file": "CallID.js"}, {"name": "Cfg", "type": "config", "file": "Cfg.js"}, {"name": "CurrencyConfigCfg", "type": "config", "file": "CurrencyConfigCfg.js"}, {"name": "dmmItemCfg", "type": "config", "file": "dmmItemCfg.js"}, {"name": "dmmRoleCfg", "type": "config", "file": "dmmRoleCfg.js"}, {"name": "dragonPathCfg", "type": "config", "file": "dragonPathCfg.js"}, {"name": "DropConfigCfg", "type": "config", "file": "DropConfigCfg.js"}, {"name": "EquipLvCfg", "type": "config", "file": "EquipLvCfg.js"}, {"name": "EquipMergeLvCfg", "type": "config", "file": "EquipMergeLvCfg.js"}, {"name": "EventController", "type": "controller", "file": "EventController.js"}, {"name": "EventModel", "type": "valueObject", "file": "EventModel.js"}, {"name": "ExchangeCodeView", "type": "view", "file": "ExchangeCodeView.js"}, {"name": "FightController", "type": "controller", "file": "FightController.js"}, {"name": "FightModel", "type": "valueObject", "file": "FightModel.js"}, {"name": "FightUIView", "type": "view", "file": "FightUIView.js"}, {"name": "GameatrCfg", "type": "config", "file": "GameatrCfg.js"}, {"name": "GameSettingCfg", "type": "config", "file": "GameSettingCfg.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "view", "file": "GridView.js"}, {"name": "Grid<PERSON>iew<PERSON>ell", "type": "view", "file": "GridViewCell.js"}, {"name": "GridViewFreshWork", "type": "view", "file": "GridViewFreshWork.js"}, {"name": "GuideCfg", "type": "config", "file": "GuideCfg.js"}, {"name": "GuidesController", "type": "controller", "file": "GuidesController.js"}, {"name": "GuidesModel", "type": "valueObject", "file": "GuidesModel.js"}, {"name": "ItemController", "type": "controller", "file": "ItemController.js"}, {"name": "ItemModel", "type": "valueObject", "file": "ItemModel.js"}, {"name": "KnapsackVo", "type": "valueObject", "file": "KnapsackVo.js"}, {"name": "languageCfg", "type": "config", "file": "languageCfg.js"}, {"name": "LevelExpCfg", "type": "config", "file": "LevelExpCfg.js"}, {"name": "ListenID", "type": "id", "file": "ListenID.js"}, {"name": "LoadingController", "type": "controller", "file": "LoadingController.js"}, {"name": "LoadingModel", "type": "valueObject", "file": "LoadingModel.js"}, {"name": "LoadingView", "type": "view", "file": "LoadingView.js"}, {"name": "LvInsideCfg", "type": "config", "file": "LvInsideCfg.js"}, {"name": "LvOutsideCfg", "type": "config", "file": "LvOutsideCfg.js"}, {"name": "M20_Pop_GameRewardView", "type": "view", "file": "M20_Pop_GameRewardView.js"}, {"name": "M20_PrePare_MenuView", "type": "view", "file": "M20_PrePare_MenuView.js"}, {"name": "M33_FightBuffView", "type": "view", "file": "M33_FightBuffView.js"}, {"name": "M33_FightUIView", "type": "view", "file": "M33_FightUIView.js"}, {"name": "MapCfg", "type": "config", "file": "MapCfg.js"}, {"name": "MiniGameEquipCfg", "type": "config", "file": "MiniGameEquipCfg.js"}, {"name": "MiniGameLvCfg", "type": "config", "file": "MiniGameLvCfg.js"}, {"name": "ModeAllOutAttackController", "type": "controller", "file": "ModeAllOutAttackController.js"}, {"name": "ModeAllOutAttackModel", "type": "valueObject", "file": "ModeAllOutAttackModel.js"}, {"name": "ModeBackpackHeroController", "type": "controller", "file": "ModeBackpackHeroController.js"}, {"name": "ModeBackpackHeroModel", "type": "valueObject", "file": "ModeBackpackHeroModel.js"}, {"name": "ModeBulletsReboundController", "type": "controller", "file": "ModeBulletsReboundController.js"}, {"name": "ModeBulletsReboundModel", "type": "valueObject", "file": "ModeBulletsReboundModel.js"}, {"name": "ModeChainsController", "type": "controller", "file": "ModeChainsController.js"}, {"name": "ModeChainsModel", "type": "valueObject", "file": "ModeChainsModel.js"}, {"name": "ModeDragonWarController", "type": "controller", "file": "ModeDragonWarController.js"}, {"name": "ModeDragonWarModel", "type": "valueObject", "file": "ModeDragonWarModel.js"}, {"name": "ModeManGuardsController", "type": "controller", "file": "ModeManGuardsController.js"}, {"name": "ModeManGuardsModel", "type": "valueObject", "file": "ModeManGuardsModel.js"}, {"name": "ModePickUpBulletsController", "type": "controller", "file": "ModePickUpBulletsController.js"}, {"name": "ModePickUpBulletsModel", "type": "valueObject", "file": "ModePickUpBulletsModel.js"}, {"name": "ModeThrowingKnifeController", "type": "controller", "file": "ModeThrowingKnifeController.js"}, {"name": "ModeThrowingKnifeModel", "type": "valueObject", "file": "ModeThrowingKnifeModel.js"}, {"name": "MonsterCfg", "type": "config", "file": "MonsterCfg.js"}, {"name": "MonsterLvCfg", "type": "config", "file": "MonsterLvCfg.js"}, {"name": "MoreGamesView", "type": "view", "file": "MoreGamesView.js"}, {"name": "NotifyID", "type": "id", "file": "NotifyID.js"}, {"name": "PayController", "type": "controller", "file": "PayController.js"}, {"name": "PayModel", "type": "valueObject", "file": "PayModel.js"}, {"name": "PayShopCfg", "type": "config", "file": "PayShopCfg.js"}, {"name": "PoolListCfg", "type": "config", "file": "PoolListCfg.js"}, {"name": "ProcessRewardsCfg", "type": "config", "file": "ProcessRewardsCfg.js"}, {"name": "PropertyVo", "type": "valueObject", "file": "PropertyVo.js"}, {"name": "randomNameCfg", "type": "config", "file": "randomNameCfg.js"}, {"name": "RBadgeController", "type": "controller", "file": "RBadgeController.js"}, {"name": "RBadgeModel", "type": "valueObject", "file": "RBadgeModel.js"}, {"name": "RecordVo", "type": "valueObject", "file": "RecordVo.js"}, {"name": "RoleCfg", "type": "config", "file": "RoleCfg.js"}, {"name": "RoleLvCfg", "type": "config", "file": "RoleLvCfg.js"}, {"name": "RoleUnlockCfg", "type": "config", "file": "RoleUnlockCfg.js"}, {"name": "SdkConfig", "type": "config", "file": "SdkConfig.js"}, {"name": "SettingController", "type": "controller", "file": "SettingController.js"}, {"name": "SettingModel", "type": "valueObject", "file": "SettingModel.js"}, {"name": "SettingView", "type": "view", "file": "SettingView.js"}, {"name": "ShopController", "type": "controller", "file": "ShopController.js"}, {"name": "ShopModel", "type": "valueObject", "file": "ShopModel.js"}, {"name": "signCfg", "type": "config", "file": "signCfg.js"}, {"name": "SkiilpoolCfg", "type": "config", "file": "SkiilpoolCfg.js"}, {"name": "SkillCfg", "type": "config", "file": "SkillCfg.js"}, {"name": "SkillController", "type": "controller", "file": "SkillController.js"}, {"name": "SkillModel", "type": "valueObject", "file": "SkillModel.js"}, {"name": "SoundCfg", "type": "config", "file": "SoundCfg.js"}, {"name": "StorageID", "type": "id", "file": "StorageID.js"}, {"name": "SwitchVo", "type": "valueObject", "file": "SwitchVo.js"}, {"name": "TaskCfg", "type": "config", "file": "TaskCfg.js"}, {"name": "TaskModel", "type": "valueObject", "file": "TaskModel.js"}, {"name": "TaskTypeCfg", "type": "config", "file": "TaskTypeCfg.js"}, {"name": "TConfig", "type": "config", "file": "TConfig.js"}, {"name": "TestController", "type": "controller", "file": "TestController.js"}, {"name": "TestModel", "type": "valueObject", "file": "TestModel.js"}, {"name": "TestView", "type": "view", "file": "TestView.js"}, {"name": "TideDefendController", "type": "controller", "file": "TideDefendController.js"}, {"name": "TideDefendModel", "type": "valueObject", "file": "TideDefendModel.js"}, {"name": "TowerAmethystRewardCfg", "type": "config", "file": "TowerAmethystRewardCfg.js"}, {"name": "TowerCfg", "type": "config", "file": "TowerCfg.js"}, {"name": "TowerCoinRewardCfg", "type": "config", "file": "TowerCoinRewardCfg.js"}, {"name": "TowerLvCfg", "type": "config", "file": "TowerLvCfg.js"}, {"name": "TowerMenuCfg", "type": "config", "file": "TowerMenuCfg.js"}, {"name": "UserVo", "type": "valueObject", "file": "UserVo.js"}, {"name": "WeatherCfg", "type": "config", "file": "WeatherCfg.js"}]}}