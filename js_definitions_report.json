{"summary": {"totalFiles": 344, "filesWithDefinitions": 329, "ccclassCount": 342, "staticClassCount": 23, "exportedFunctionCount": 25, "enumCount": 15, "otherDefinitionCount": 339}, "details": {"ccclasses": [{"name": "exp_activityCfgReader", "variableName": "exp_activityCfgReader", "extends": "e", "file": "activityCfg.js"}, {"name": "exp_ADController", "variableName": "exp_ADController", "extends": "e", "file": "ADController.js"}, {"name": "ADModel", "variableName": "def_ADModel", "extends": "e", "file": "ADModel.js"}, {"name": "exp_adRewardCfgReader", "variableName": "exp_adRewardCfgReader", "extends": "e", "file": "adRewardCfg.js"}, {"name": "ArcBullet", "variableName": "_ctor", "extends": "e", "file": "ArcBullet.js"}, {"name": "AutoAmTool", "variableName": "_ctor", "extends": "e", "file": "AutoAmTool.js"}, {"name": "AutoAnimationClip", "variableName": "_ctor", "extends": "e", "file": "AutoAnimationClip.js"}, {"name": "AutoFollow", "variableName": "_ctor", "extends": "e", "file": "AutoFollow.js"}, {"name": "BackHeroProp", "variableName": "_ctor", "extends": "e", "file": "BackHeroProp.js"}, {"name": "BackpackHeroHome", "variableName": "_ctor", "extends": "e", "file": "BackpackHeroHome.js"}, {"name": "exp_BagBuffCfgReader", "variableName": "exp_BagBuffCfgReader", "extends": "e", "file": "BagBuffCfg.js"}, {"name": "exp_BagGuideCfgReader", "variableName": "exp_BagGuideCfgReader", "extends": "e", "file": "BagGuideCfg.js"}, {"name": "exp_BagModeLvCfgReader", "variableName": "exp_BagModeLvCfgReader", "extends": "e", "file": "BagModeLvCfg.js"}, {"name": "exp_BagModeSkillPoolCfgReader", "variableName": "exp_BagModeSkillPoolCfgReader", "extends": "e", "file": "BagModeSkillPoolCfg.js"}, {"name": "exp_bagMonsterLvCfgReader", "variableName": "exp_bagMonsterLvCfgReader", "extends": "e", "file": "bagMonsterLvCfg.js"}, {"name": "exp_BagShopItemCfgReader", "variableName": "exp_BagShopItemCfgReader", "extends": "e", "file": "BagShopItemCfg.js"}, {"name": "exp_BagSkillCfgReader", "variableName": "exp_BagSkillCfgReader", "extends": "e", "file": "BagSkillCfg.js"}, {"name": "BaseEntity", "variableName": "o", "extends": null, "file": "BaseEntity.js"}, {"name": "BoomerangBullet", "variableName": "_ctor", "extends": "e", "file": "BoomerangBullet.js"}, {"name": "exp_BottomBarController", "variableName": "exp_BottomBarController", "extends": "e", "file": "BottomBarController.js"}, {"name": "BottomBarModel", "variableName": "def_BottomBarModel", "extends": "e", "file": "BottomBarModel.js"}, {"name": "BottomBarView", "variableName": "_ctor", "extends": "e", "file": "BottomBarView.js"}, {"name": "exp_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "variableName": "exp_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "extends": "e", "file": "BottomBarView.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "variableName": "_ctor", "extends": "e", "file": "BounceBullet.js"}, {"name": "exp_BoxLevelExpCfgReader", "variableName": "exp_BoxLevelExpCfgReader", "extends": "e", "file": "BoxLevelExpCfg.js"}, {"name": "exp_BronMonsterManger", "variableName": "exp_BronMonsterManger", "extends": "e", "file": "BronMonsterManger.js"}, {"name": "BuffCardItem", "variableName": "_ctor", "extends": "e", "file": "BuffCardItem.js"}, {"name": "exp_BuffCfgReader", "variableName": "exp_BuffCfgReader", "extends": "e", "file": "BuffCfg.js"}, {"name": "exp_BuffController", "variableName": "exp_BuffController", "extends": "e", "file": "BuffController.js"}, {"name": "BuffList", "variableName": "_ctor", "extends": "e", "file": "BuffList.js"}, {"name": "exp_<PERSON><PERSON>_<PERSON>", "variableName": "exp_<PERSON><PERSON>_<PERSON>", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_Excute", "variableName": "exp_Buff_Excute", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_OnTime", "variableName": "exp_Buff_OnTime", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_Effect", "variableName": "exp_Buff_Effect", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_OnSpawnHurt", "variableName": "exp_Buff_OnSpawnHurt", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_OnVampirism", "variableName": "exp_Buff_OnVampirism", "extends": "e", "file": "BuffList.js"}, {"name": "exp_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>Rew<PERSON>", "variableName": "exp_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>Rew<PERSON>", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_OnBehit", "variableName": "exp_Buff_OnBehit", "extends": "e", "file": "BuffList.js"}, {"name": "exp_B<PERSON>_<PERSON>", "variableName": "exp_B<PERSON>_<PERSON>", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_HPLink", "variableName": "exp_Buff_HPLink", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_ContinuousRecovery", "variableName": "exp_Buff_ContinuousRecovery", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_HPLinkOnce", "variableName": "exp_Buff_HPLinkOnce", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_EntityDead", "variableName": "exp_Buff_EntityDead", "extends": "e", "file": "BuffList.js"}, {"name": "exp_B<PERSON>_VicinityHurt", "variableName": "exp_B<PERSON>_VicinityHurt", "extends": "e", "file": "BuffList.js"}, {"name": "exp_<PERSON><PERSON>_<PERSON>o", "variableName": "exp_<PERSON><PERSON>_<PERSON>o", "extends": "e", "file": "BuffList.js"}, {"name": "exp_B<PERSON>_<PERSON>", "variableName": "exp_B<PERSON>_<PERSON>", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_OnUseSkillHurt", "variableName": "exp_Buff_OnUseSkillHurt", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_SubSkill", "variableName": "exp_Buff_SubSkill", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_AtkFocus", "variableName": "exp_Buff_AtkFocus", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_AdrenalTechnology", "variableName": "exp_Buff_AdrenalTechnology", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_OnSkillUseUnload", "variableName": "exp_Buff_OnSkillUseUnload", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_ReboundDam", "variableName": "exp_Buff_ReboundDam", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_OnKillLayout", "variableName": "exp_Buff_OnKillLayout", "extends": "e", "file": "BuffList.js"}, {"name": "exp_B<PERSON>_HitBack", "variableName": "exp_B<PERSON>_HitBack", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_OnLifeVal", "variableName": "exp_Buff_OnLifeVal", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_OnSpawnHurtAddArmor", "variableName": "exp_Buff_OnSpawnHurtAddArmor", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_OnBehitAddArmor", "variableName": "exp_Buff_OnBehitAddArmor", "extends": "e", "file": "BuffList.js"}, {"name": "exp_B<PERSON>_RestoreArmor", "variableName": "exp_B<PERSON>_RestoreArmor", "extends": "e", "file": "BuffList.js"}, {"name": "exp_B<PERSON>_OnSkill", "variableName": "exp_B<PERSON>_OnSkill", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_Vampire", "variableName": "exp_Buff_Vampire", "extends": "e", "file": "BuffList.js"}, {"name": "exp_<PERSON>uff_AssociationProp", "variableName": "exp_<PERSON>uff_AssociationProp", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_ResistDamage", "variableName": "exp_Buff_ResistDamage", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_SetSlash", "variableName": "exp_Buff_SetSlash", "extends": "e", "file": "BuffList.js"}, {"name": "exp_Buff_OnRoundState", "variableName": "exp_Buff_OnRoundState", "extends": "e", "file": "BuffList.js"}, {"name": "exp_B<PERSON>_<PERSON>laceRole", "variableName": "exp_B<PERSON>_<PERSON>laceRole", "extends": "e", "file": "BuffList.js"}, {"name": "BuffModel", "variableName": "def_BuffModel", "extends": "e", "file": "BuffModel.js"}, {"name": "exp_BuildModeSkiilpoolCfgReader", "variableName": "exp_BuildModeSkiilpoolCfgReader", "extends": "e", "file": "BuildModeSkiilpoolCfg.js"}, {"name": "Bullet", "variableName": "_ctor", "extends": "e", "file": "Bullet.js"}, {"name": "BulletBase", "variableName": "_ctor", "extends": "e", "file": "BulletBase.js"}, {"name": "exp_BulletEffectCfgReader", "variableName": "exp_BulletEffectCfgReader", "extends": "e", "file": "BulletEffectCfg.js"}, {"name": "Bullet_Arrow", "variableName": "_ctor", "extends": "e", "file": "Bullet_Arrow.js"}, {"name": "Bullet_FollowTarget", "variableName": "_ctor", "extends": "e", "file": "Bullet_FollowTarget.js"}, {"name": "Bullet_HitReflex", "variableName": "_ctor", "extends": "e", "file": "Bullet_HitReflex.js"}, {"name": "Bullet_Laser", "variableName": "_ctor", "extends": "e", "file": "Bullet_Laser.js"}, {"name": "Bullet_Ligature", "variableName": "_ctor", "extends": "e", "file": "Bullet_Ligature.js"}, {"name": "Bullet_LigaturePonit", "variableName": "_ctor", "extends": "e", "file": "Bullet_LigaturePonit.js"}, {"name": "Bullet_Path", "variableName": "_ctor", "extends": "e", "file": "Bullet_Path.js"}, {"name": "Bullet_RandomMove", "variableName": "_ctor", "extends": "e", "file": "Bullet_RandomMove.js"}, {"name": "Bullet_RigidBody", "variableName": "_ctor", "extends": "e", "file": "Bullet_RigidBody.js"}, {"name": "ByteDance", "variableName": "def_ByteDance", "extends": "e", "file": "ByteDance.js"}, {"name": "CircleBullet", "variableName": "_ctor", "extends": "e", "file": "CircleBullet.js"}, {"name": "Commonguide", "variableName": "o", "extends": null, "file": "Commonguide.js"}, {"name": "Continuous<PERSON>ullet", "variableName": "_ctor", "extends": "e", "file": "ContinuousBullet.js"}, {"name": "exp_CurrencyConfigCfgReader", "variableName": "exp_CurrencyConfigCfgReader", "extends": "e", "file": "CurrencyConfigCfg.js"}, {"name": "<PERSON><PERSON><PERSON>cyT<PERSON><PERSON>", "variableName": "_ctor", "extends": "e", "file": "CurrencyTips.js"}, {"name": "DialogBox", "variableName": "_ctor", "extends": "e", "file": "DialogBox.js"}, {"name": "exp_dmmItemCfgReader", "variableName": "exp_dmmItemCfgReader", "extends": "e", "file": "dmmItemCfg.js"}, {"name": "exp_dmmRoleCfgReader", "variableName": "exp_dmmRoleCfgReader", "extends": "e", "file": "dmmRoleCfg.js"}, {"name": "Dragon", "variableName": "_ctor", "extends": "e", "file": "Dragon.js"}, {"name": "exp_Dragon", "variableName": "exp_Dragon", "extends": "e", "file": "Dragon.js"}, {"name": "DragonBody", "variableName": "_ctor", "extends": "e", "file": "DragonBody.js"}, {"name": "exp_dragonPathCfgReader", "variableName": "exp_dragonPathCfgReader", "extends": "e", "file": "dragonPathCfg.js"}, {"name": "exp_DropConfigCfgReader", "variableName": "exp_DropConfigCfgReader", "extends": "e", "file": "DropConfigCfg.js"}, {"name": "exp_EaseScaleTransition", "variableName": "exp_EaseScaleTransition", "extends": "e", "file": "EaseScaleTransition.js"}, {"name": "EffectSkeleton", "variableName": "_ctor", "extends": "e", "file": "EffectSkeleton.js"}, {"name": "Effect_Behead", "variableName": "_ctor", "extends": "e", "file": "Effect_Behead.js"}, {"name": "Effect_Behit", "variableName": "_ctor", "extends": "e", "file": "Effect_Behit.js"}, {"name": "EnergyStamp", "variableName": "o", "extends": null, "file": "EnergyStamp.js"}, {"name": "EntityDieEffect", "variableName": "_ctor", "extends": "e", "file": "EntityDieEffect.js"}, {"name": "exp_EquipLvCfgReader", "variableName": "exp_EquipLvCfgReader", "extends": "e", "file": "EquipLvCfg.js"}, {"name": "exp_EquipMergeLvCfgReader", "variableName": "exp_EquipMergeLvCfgReader", "extends": "e", "file": "EquipMergeLvCfg.js"}, {"name": "exp_EventController", "variableName": "exp_EventController", "extends": "e", "file": "EventController.js"}, {"name": "EventModel", "variableName": "def_EventModel", "extends": "e", "file": "EventModel.js"}, {"name": "ExchangeCodeView", "variableName": "_ctor", "extends": "e", "file": "ExchangeCodeView.js"}, {"name": "ExSprite", "variableName": "_ctor", "extends": "e", "file": "ExSprite.js"}, {"name": "FBoxCollider", "variableName": "_ctor", "extends": "e", "file": "FBoxCollider.js"}, {"name": "FCircleCollider", "variableName": "_ctor", "extends": "e", "file": "FCircleCollider.js"}, {"name": "FCollider", "variableName": "o", "extends": null, "file": "FCollider.js"}, {"name": "exp_FightController", "variableName": "exp_FightController", "extends": "e", "file": "FightController.js"}, {"name": "FightModel", "variableName": "def_FightModel", "extends": "e", "file": "FightModel.js"}, {"name": "FightScene", "variableName": "_ctor", "extends": "e", "file": "FightScene.js"}, {"name": "exp_FightScene", "variableName": "exp_FightScene", "extends": "e", "file": "FightScene.js"}, {"name": "FightUIView", "variableName": "_ctor", "extends": "e", "file": "FightUIView.js"}, {"name": "exp_FightUIView", "variableName": "exp_FightUIView", "extends": "e", "file": "FightUIView.js"}, {"name": "FPolygonCollider", "variableName": "_ctor", "extends": "e", "file": "FPolygonCollider.js"}, {"name": "GameAnimi", "variableName": "_ctor", "extends": "e", "file": "GameAnimi.js"}, {"name": "exp_GameatrCfgReader", "variableName": "exp_GameatrCfgReader", "extends": "e", "file": "GameatrCfg.js"}, {"name": "exp_GameCamera", "variableName": "exp_GameCamera", "extends": "e", "file": "GameCamera.js"}, {"name": "GameEffect", "variableName": "_ctor", "extends": "e", "file": "GameEffect.js"}, {"name": "exp_GameSettingCfgReader", "variableName": "exp_GameSettingCfgReader", "extends": "e", "file": "GameSettingCfg.js"}, {"name": "GameSkeleton", "variableName": "o", "extends": null, "file": "GameSkeleton.js"}, {"name": "Goods", "variableName": "_ctor", "extends": "e", "file": "Goods.js"}, {"name": "GoodsUIItem", "variableName": "o", "extends": null, "file": "GoodsUIItem.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variableName": "_ctor", "extends": "e", "file": "GridView.js"}, {"name": "Grid<PERSON>iew<PERSON>ell", "variableName": "_ctor", "extends": "e", "file": "GridViewCell.js"}, {"name": "GTAssembler2D", "variableName": "def_GTAssembler2D", "extends": "e", "file": "GTAssembler2D.js"}, {"name": "GTSimpleSpriteAssembler2D", "variableName": "def_GTSimpleSpriteAssembler2D", "extends": "e", "file": "GTSimpleSpriteAssembler2D.js"}, {"name": "exp_GuideCfgReader", "variableName": "exp_GuideCfgReader", "extends": "e", "file": "GuideCfg.js"}, {"name": "exp_GuidesController", "variableName": "exp_GuidesController", "extends": "e", "file": "GuidesController.js"}, {"name": "GuidesModel", "variableName": "def_GuidesModel", "extends": "e", "file": "GuidesModel.js"}, {"name": "IOSSdk", "variableName": "def_IOSSdk", "extends": "e", "file": "IOSSdk.js"}, {"name": "exp_ItemController", "variableName": "exp_ItemController", "extends": "e", "file": "ItemController.js"}, {"name": "ItemModel", "variableName": "def_ItemModel", "extends": "e", "file": "ItemModel.js"}, {"name": "JUHEAndroid", "variableName": "def_JUHEAndroid", "extends": "e", "file": "JUHEAndroid.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "variableName": "_ctor", "extends": "e", "file": "KawaseAnim.js"}, {"name": "exp_languageCfgReader", "variableName": "exp_languageCfgReader", "extends": "e", "file": "languageCfg.js"}, {"name": "LaserRadiationBullet", "variableName": "_ctor", "extends": "e", "file": "LaserRadiationBullet.js"}, {"name": "Launcher", "variableName": "_ctor", "extends": "e", "file": "Launcher.js"}, {"name": "exp_LevelExpCfgReader", "variableName": "exp_LevelExpCfgReader", "extends": "e", "file": "LevelExpCfg.js"}, {"name": "LevelMgr", "variableName": "t", "extends": "e", "file": "LevelMgr.js"}, {"name": "LifeBar", "variableName": "_ctor", "extends": "e", "file": "LifeBar.js"}, {"name": "LifeLabel", "variableName": "_ctor", "extends": "e", "file": "LifeLabel.js"}, {"name": "LigatureBullet", "variableName": "_ctor", "extends": "e", "file": "LigatureBullet.js"}, {"name": "exp_LoadingController", "variableName": "exp_LoadingController", "extends": "e", "file": "LoadingController.js"}, {"name": "LoadingModel", "variableName": "def_LoadingModel", "extends": "e", "file": "LoadingModel.js"}, {"name": "LoadingView", "variableName": "_ctor", "extends": "e", "file": "LoadingView.js"}, {"name": "exp_LoadingView", "variableName": "exp_LoadingView", "extends": "e", "file": "LoadingView.js"}, {"name": "exp_LvInsideCfgReader", "variableName": "exp_LvInsideCfgReader", "extends": "e", "file": "LvInsideCfg.js"}, {"name": "exp_LvOutsideCfgReader", "variableName": "exp_LvOutsideCfgReader", "extends": "e", "file": "LvOutsideCfg.js"}, {"name": "M20Equipitem", "variableName": "_ctor", "extends": "e", "file": "M20Equipitem.js"}, {"name": "M20EquipitemBlock", "variableName": "_ctor", "extends": "e", "file": "M20EquipitemBlock.js"}, {"name": "M20EquipitemList", "variableName": "_ctor", "extends": "e", "file": "M20EquipitemList.js"}, {"name": "M20Gooditem", "variableName": "_ctor", "extends": "e", "file": "M20Gooditem.js"}, {"name": "M20Prop", "variableName": "_ctor", "extends": "e", "file": "M20Prop.js"}, {"name": "M20Prop_Equip", "variableName": "o", "extends": null, "file": "M20Prop_Equip.js"}, {"name": "M20Prop_Gemstone", "variableName": "o", "extends": null, "file": "M20Prop_Gemstone.js"}, {"name": "M20_PartItem", "variableName": "_ctor", "extends": "e", "file": "M20_PartItem.js"}, {"name": "M20_Pop_EquipInfo", "variableName": "_ctor", "extends": "e", "file": "M20_Pop_EquipInfo.js"}, {"name": "M20_Pop_GameRewardView", "variableName": "_ctor", "extends": "e", "file": "M20_Pop_GameRewardView.js"}, {"name": "M20_Pop_GetBox", "variableName": "_ctor", "extends": "e", "file": "M20_Pop_GetBox.js"}, {"name": "M20_Pop_GetEnergy", "variableName": "_ctor", "extends": "e", "file": "M20_Pop_GetEnergy.js"}, {"name": "M20_Pop_Insufficient_Props_Tips", "variableName": "_ctor", "extends": "e", "file": "M20_Pop_Insufficient_Props_Tips.js"}, {"name": "M20_Pop_NewEquipUnlock", "variableName": "_ctor", "extends": "e", "file": "M20_Pop_NewEquipUnlock.js"}, {"name": "M20_Pop_ShopBoxInfo", "variableName": "_ctor", "extends": "e", "file": "M20_Pop_ShopBoxInfo.js"}, {"name": "M20_Pop_ShopBuyConfirm", "variableName": "_ctor", "extends": "e", "file": "M20_Pop_ShopBuyConfirm.js"}, {"name": "M20_PrePare_Activity", "variableName": "_ctor", "extends": "e", "file": "M20_PrePare_Activity.js"}, {"name": "M20_PrePare_Equip", "variableName": "_ctor", "extends": "e", "file": "M20_PrePare_Equip.js"}, {"name": "M20_PrePare_Fight", "variableName": "_ctor", "extends": "e", "file": "M20_PrePare_Fight.js"}, {"name": "M20_PrePare_MenuView", "variableName": "_ctor", "extends": "e", "file": "M20_PrePare_MenuView.js"}, {"name": "M20_PrePare_Shop", "variableName": "_ctor", "extends": "e", "file": "M20_PrePare_Shop.js"}, {"name": "M20_ShopPartItem", "variableName": "_ctor", "extends": "e", "file": "M20_ShopPartItem.js"}, {"name": "M20_ShopPartItem_adcoupon", "variableName": "_ctor", "extends": "e", "file": "M20_ShopPartItem_adcoupon.js"}, {"name": "M20_ShopPartItem_box", "variableName": "_ctor", "extends": "e", "file": "M20_ShopPartItem_box.js"}, {"name": "M20_ShopPartItem_coin", "variableName": "_ctor", "extends": "e", "file": "M20_ShopPartItem_coin.js"}, {"name": "M20_ShopPartItem_daily", "variableName": "_ctor", "extends": "e", "file": "M20_ShopPartItem_daily.js"}, {"name": "M20_ShopPartItem_hero", "variableName": "_ctor", "extends": "e", "file": "M20_ShopPartItem_hero.js"}, {"name": "M20_Shop_HeroItem", "variableName": "_ctor", "extends": "e", "file": "M20_Shop_HeroItem.js"}, {"name": "M33_FightBuffView", "variableName": "_ctor", "extends": "e", "file": "M33_FightBuffView.js"}, {"name": "M33_FightScene", "variableName": "_ctor", "extends": "e", "file": "M33_FightScene.js"}, {"name": "exp_M33_FightScene", "variableName": "exp_M33_FightScene", "extends": "e", "file": "M33_FightScene.js"}, {"name": "M33_FightUIView", "variableName": "_ctor", "extends": "e", "file": "M33_FightUIView.js"}, {"name": "exp_M33_FightUIView", "variableName": "exp_M33_FightUIView", "extends": "e", "file": "M33_FightUIView.js"}, {"name": "M33_Pop_DiffSelectGeneral", "variableName": "_ctor", "extends": "e", "file": "M33_Pop_DiffSelectGeneral.js"}, {"name": "M33_Pop_GameEnd", "variableName": "_ctor", "extends": "e", "file": "M33_Pop_GameEnd.js"}, {"name": "M33_Pop_Revive", "variableName": "_ctor", "extends": "e", "file": "M33_Pop_Revive.js"}, {"name": "M33_TestBox", "variableName": "_ctor", "extends": "e", "file": "M33_TestBox.js"}, {"name": "exp_MapCfgReader", "variableName": "exp_MapCfgReader", "extends": "e", "file": "MapCfg.js"}, {"name": "MBRMonster", "variableName": "_ctor", "extends": "e", "file": "MBRMonster.js"}, {"name": "MBRRole", "variableName": "_ctor", "extends": "e", "file": "MBRRole.js"}, {"name": "MCBoss", "variableName": "o", "extends": null, "file": "MCBoss.js"}, {"name": "exp_MC<PERSON>oss", "variableName": "exp_MC<PERSON>oss", "extends": "e", "file": "MCBoss.js"}, {"name": "MCBossState", "variableName": "t", "extends": "e", "file": "MCBossState.js"}, {"name": "MCDragoMutilation", "variableName": "_ctor", "extends": "e", "file": "MCDragoMutilation.js"}, {"name": "exp_MCDragoMutilation", "variableName": "exp_MCDragoMutilation", "extends": "e", "file": "MCDragoMutilation.js"}, {"name": "MCDragon", "variableName": "_ctor", "extends": "e", "file": "MCDragon.js"}, {"name": "exp_MCDragon", "variableName": "exp_MCDragon", "extends": "e", "file": "MCDragon.js"}, {"name": "MCPet", "variableName": "_ctor", "extends": "e", "file": "MCPet.js"}, {"name": "MCRole", "variableName": "_ctor", "extends": "e", "file": "MCRole.js"}, {"name": "exp_MiniGameEquipCfgReader", "variableName": "exp_MiniGameEquipCfgReader", "extends": "e", "file": "MiniGameEquipCfg.js"}, {"name": "exp_MiniGameLvCfgReader", "variableName": "exp_MiniGameLvCfgReader", "extends": "e", "file": "MiniGameLvCfg.js"}, {"name": "MMGMonster", "variableName": "_ctor", "extends": "e", "file": "MMGMonster.js"}, {"name": "MMGRole", "variableName": "_ctor", "extends": "e", "file": "MMGRole.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "variableName": "o", "extends": "e", "file": "MMGuards.js"}, {"name": "exp_ModeAllOutAttackController", "variableName": "exp_ModeAllOutAttackController", "extends": "e", "file": "ModeAllOutAttackController.js"}, {"name": "ModeAllOutAttackModel", "variableName": "def_ModeAllOutAttackModel", "extends": "e", "file": "ModeAllOutAttackModel.js"}, {"name": "exp_ModeBackpackHeroController", "variableName": "exp_ModeBackpackHeroController", "extends": "e", "file": "ModeBackpackHeroController.js"}, {"name": "ModeBackpackHeroModel", "variableName": "w", "extends": "e", "file": "ModeBackpackHeroModel.js"}, {"name": "exp_ModeBulletsReboundController", "variableName": "exp_ModeBulletsReboundController", "extends": "e", "file": "ModeBulletsReboundController.js"}, {"name": "ModeBulletsReboundModel", "variableName": "def_ModeBulletsReboundModel", "extends": "e", "file": "ModeBulletsReboundModel.js"}, {"name": "exp_ModeChainsController", "variableName": "exp_ModeChainsController", "extends": "e", "file": "ModeChainsController.js"}, {"name": "ModeChainsModel", "variableName": "y", "extends": "e", "file": "ModeChainsModel.js"}, {"name": "exp_ModeDragonWarController", "variableName": "exp_ModeDragonWarController", "extends": "e", "file": "ModeDragonWarController.js"}, {"name": "ModeDragonWarModel", "variableName": "def_ModeDragonWarModel", "extends": "e", "file": "ModeDragonWarModel.js"}, {"name": "exp_ModeManGuardsController", "variableName": "exp_ModeManGuardsController", "extends": "e", "file": "ModeManGuardsController.js"}, {"name": "ModeManGuardsModel", "variableName": "def_ModeManGuardsModel", "extends": "e", "file": "ModeManGuardsModel.js"}, {"name": "exp_ModePickUpBulletsController", "variableName": "exp_ModePickUpBulletsController", "extends": "e", "file": "ModePickUpBulletsController.js"}, {"name": "ModePickUpBulletsModel", "variableName": "def_ModePickUpBulletsModel", "extends": "e", "file": "ModePickUpBulletsModel.js"}, {"name": "exp_ModeThrowingKnifeController", "variableName": "exp_ModeThrowingKnifeController", "extends": "e", "file": "ModeThrowingKnifeController.js"}, {"name": "ModeThrowingKnifeModel", "variableName": "def_ModeThrowingKnifeModel", "extends": "e", "file": "ModeThrowingKnifeModel.js"}, {"name": "MonstarTideDragon", "variableName": "def_MonstarTideDragon", "extends": "e", "file": "MonstarTideDragon.js"}, {"name": "exp_Monster", "variableName": "exp_Monster", "extends": "e", "file": "Monster.js"}, {"name": "exp_MonsterCfgReader", "variableName": "exp_MonsterCfgReader", "extends": "e", "file": "MonsterCfg.js"}, {"name": "MonsterElite", "variableName": "def_MonsterElite", "extends": "e", "file": "MonsterElite.js"}, {"name": "exp_MonsterLvCfgReader", "variableName": "exp_MonsterLvCfgReader", "extends": "e", "file": "MonsterLvCfg.js"}, {"name": "MonsterState", "variableName": "t", "extends": "e", "file": "MonsterState.js"}, {"name": "MonsterTidal", "variableName": "def_MonsterTidal", "extends": "e", "file": "MonsterTidal.js"}, {"name": "MonsterTidalBoss", "variableName": "def_Monster<PERSON><PERSON><PERSON>oss", "extends": "e", "file": "MonsterTidalBoss.js"}, {"name": "MonsterTidalState", "variableName": "t", "extends": "e", "file": "MonsterTidalState.js"}, {"name": "MonsterTideDefend", "variableName": "_ctor", "extends": "e", "file": "MonsterTideDefend.js"}, {"name": "MoreGamesItem", "variableName": "_ctor", "extends": "e", "file": "MoreGamesItem.js"}, {"name": "MoreGamesView", "variableName": "_ctor", "extends": "e", "file": "MoreGamesView.js"}, {"name": "MoveEntity", "variableName": "_ctor", "extends": "e", "file": "MoveEntity.js"}, {"name": "MoveImg", "variableName": "_ctor", "extends": "e", "file": "MoveImg.js"}, {"name": "MovingBGAssembler", "variableName": "def_MovingBGAssembler", "extends": "e", "file": "MovingBGAssembler.js"}, {"name": "MovingBGSprite", "variableName": "_ctor", "extends": "e", "file": "MovingBGSprite.js"}, {"name": "MTideDefendRmod", "variableName": "_ctor", "extends": "e", "file": "MTideDefendRmod.js"}, {"name": "MTKnife", "variableName": "o", "extends": "t", "file": "MTKnife.js"}, {"name": "MTKRole", "variableName": "_ctor", "extends": "e", "file": "MTKRole.js"}, {"name": "MVC", "variableName": "t", "extends": "e", "file": "MVC.js"}, {"name": "NativeAndroid", "variableName": "def_NativeAndroid", "extends": "e", "file": "NativeAndroid.js"}, {"name": "NormalTips", "variableName": "_ctor", "extends": "e", "file": "NormalTips.js"}, {"name": "NPC", "variableName": "_ctor", "extends": "e", "file": "NPC.js"}, {"name": "OrganismBase", "variableName": "_ctor", "extends": "e", "file": "OrganismBase.js"}, {"name": "exp_PayController", "variableName": "exp_PayController", "extends": "e", "file": "PayController.js"}, {"name": "PayModel", "variableName": "def_PayModel", "extends": "e", "file": "PayModel.js"}, {"name": "exp_PayShopCfgReader", "variableName": "exp_PayShopCfgReader", "extends": "e", "file": "PayShopCfg.js"}, {"name": "Pet", "variableName": "_ctor", "extends": "e", "file": "Pet.js"}, {"name": "PetState", "variableName": "t", "extends": "e", "file": "PetState.js"}, {"name": "exp_PoolListCfgReader", "variableName": "exp_PoolListCfgReader", "extends": "e", "file": "PoolListCfg.js"}, {"name": "Pop", "variableName": "_ctor", "extends": "e", "file": "Pop.js"}, {"name": "exp_Pop", "variableName": "exp_Pop", "extends": "e", "file": "Pop.js"}, {"name": "exp_ProcessRewardsCfgReader", "variableName": "exp_ProcessRewardsCfgReader", "extends": "e", "file": "ProcessRewardsCfg.js"}, {"name": "PropertyVo", "variableName": "o", "extends": "e", "file": "PropertyVo.js"}, {"name": "exp_randomNameCfgReader", "variableName": "exp_randomNameCfgReader", "extends": "e", "file": "randomNameCfg.js"}, {"name": "exp_RBadgeController", "variableName": "exp_RBadgeController", "extends": "e", "file": "RBadgeController.js"}, {"name": "RBadgeModel", "variableName": "i", "extends": "e", "file": "RBadgeModel.js"}, {"name": "RBadgePoint", "variableName": "_ctor", "extends": "e", "file": "RBadgePoint.js"}, {"name": "ReflexBullet", "variableName": "_ctor", "extends": "e", "file": "ReflexBullet.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variableName": "_ctor", "extends": "e", "file": "ResKeeper.js"}, {"name": "Role", "variableName": "_ctor", "extends": "e", "file": "Role.js"}, {"name": "exp_RoleCfgReader", "variableName": "exp_RoleCfgReader", "extends": "e", "file": "RoleCfg.js"}, {"name": "exp_RoleLvCfgReader", "variableName": "exp_RoleLvCfgReader", "extends": "e", "file": "RoleLvCfg.js"}, {"name": "RoleSkillList", "variableName": "_ctor", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_<PERSON><PERSON>_<PERSON><PERSON>ult", "variableName": "exp_<PERSON><PERSON>_<PERSON><PERSON>ult", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_AtkDefault", "variableName": "exp_Skill_AtkDefault", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_NormalChop", "variableName": "exp_Skill_NormalChop", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_<PERSON><PERSON>_MagicBall", "variableName": "exp_<PERSON><PERSON>_MagicBall", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_Multishot", "variableName": "exp_Skill_Multishot", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_FireOffset", "variableName": "exp_Skill_FireOffset", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_<PERSON><PERSON>_ParallelFire", "variableName": "exp_<PERSON><PERSON>_ParallelFire", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_<PERSON><PERSON>_<PERSON>per", "variableName": "exp_<PERSON><PERSON>_<PERSON>per", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_MultiBullet", "variableName": "exp_Skill_MultiBullet", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_StruckLightning", "variableName": "exp_Skill_StruckLightning", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_Meteorite", "variableName": "exp_Skill_Meteorite", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_RingFireballs", "variableName": "exp_Skill_RingFireballs", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_RangeBomb", "variableName": "exp_Skill_RangeBomb", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_LaserRadiation", "variableName": "exp_Skill_LaserRadiation", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_LaserAnacampsis", "variableName": "exp_Skill_LaserAnacampsis", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_Ligature", "variableName": "exp_Skill_Ligature", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_LaserRadiationGuard", "variableName": "exp_Skill_LaserRadiationGuard", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_<PERSON><PERSON>_<PERSON>s", "variableName": "exp_<PERSON><PERSON>_<PERSON>s", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_Icicle", "variableName": "exp_Skill_Icicle", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_BounceThrowingKnife", "variableName": "exp_Skill_BounceThrowingKnife", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_Whirlwind", "variableName": "exp_Skill_Whirlwind", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_Tornado", "variableName": "exp_Skill_Tornado", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_Continuous", "variableName": "exp_Skill_Continuous", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_<PERSON>ll_GoldenCudgel", "variableName": "exp_<PERSON>ll_GoldenCudgel", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_SwordSword", "variableName": "exp_Skill_SwordSword", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_EffectSkill", "variableName": "exp_Skill_EffectSkill", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_ColdAir", "variableName": "exp_Skill_ColdAir", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_PosExcute", "variableName": "exp_Skill_PosExcute", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_StampSweep", "variableName": "exp_Skill_StampSweep", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_PowerStorage", "variableName": "exp_Skill_PowerStorage", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_<PERSON>ll_Flower", "variableName": "exp_<PERSON>ll_Flower", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_FollowPath", "variableName": "exp_Skill_FollowPath", "extends": "e", "file": "RoleSkillList.js"}, {"name": "exp_Skill_MCDragonFollowPath", "variableName": "exp_Skill_MCDragonFollowPath", "extends": "e", "file": "RoleSkillList.js"}, {"name": "RoleState", "variableName": "t", "extends": "e", "file": "RoleState.js"}, {"name": "exp_RoleUnlockCfgReader", "variableName": "exp_RoleUnlockCfgReader", "extends": "e", "file": "RoleUnlockCfg.js"}, {"name": "Select<PERSON>lert", "variableName": "_ctor", "extends": "e", "file": "SelectAlert.js"}, {"name": "exp_SettingController", "variableName": "exp_SettingController", "extends": "e", "file": "SettingController.js"}, {"name": "SettingModel", "variableName": "def_SettingModel", "extends": "e", "file": "SettingModel.js"}, {"name": "SettingView", "variableName": "_ctor", "extends": "e", "file": "SettingView.js"}, {"name": "exp_SettingView", "variableName": "exp_SettingView", "extends": "e", "file": "SettingView.js"}, {"name": "exp_ShopController", "variableName": "exp_ShopController", "extends": "e", "file": "ShopController.js"}, {"name": "ShopModel", "variableName": "f", "extends": "e", "file": "ShopModel.js"}, {"name": "exp_signCfgReader", "variableName": "exp_signCfgReader", "extends": "e", "file": "signCfg.js"}, {"name": "SkeletonBullet", "variableName": "_ctor", "extends": "e", "file": "SkeletonBullet.js"}, {"name": "exp_SkiilpoolCfgReader", "variableName": "exp_SkiilpoolCfgReader", "extends": "e", "file": "SkiilpoolCfg.js"}, {"name": "exp_SkillCfgReader", "variableName": "exp_SkillCfgReader", "extends": "e", "file": "SkillCfg.js"}, {"name": "exp_SkillController", "variableName": "exp_SkillController", "extends": "e", "file": "SkillController.js"}, {"name": "SkillModel", "variableName": "def_SkillModel", "extends": "e", "file": "SkillModel.js"}, {"name": "SkillModule", "variableName": "_ctor", "extends": null, "file": "SkillModule.js"}, {"name": "exp_SoundCfgReader", "variableName": "exp_SoundCfgReader", "extends": "e", "file": "SoundCfg.js"}, {"name": "exp_TaskCfgReader", "variableName": "exp_TaskCfgReader", "extends": "e", "file": "TaskCfg.js"}, {"name": "TaskModel", "variableName": "def_TaskModel", "extends": "e", "file": "TaskModel.js"}, {"name": "exp_TaskTypeCfgReader", "variableName": "exp_TaskTypeCfgReader", "extends": "e", "file": "TaskTypeCfg.js"}, {"name": "exp_TestController", "variableName": "exp_TestController", "extends": "e", "file": "TestController.js"}, {"name": "TestItem", "variableName": "_ctor", "extends": "e", "file": "TestItem.js"}, {"name": "TestModel", "variableName": "def_TestModel", "extends": "e", "file": "TestModel.js"}, {"name": "TestView", "variableName": "_ctor", "extends": "e", "file": "TestView.js"}, {"name": "exp_TestView", "variableName": "exp_TestView", "extends": "e", "file": "TestView.js"}, {"name": "ThrowBullet", "variableName": "_ctor", "extends": "e", "file": "ThrowBullet.js"}, {"name": "exp_TideDefendController", "variableName": "exp_TideDefendController", "extends": "e", "file": "TideDefendController.js"}, {"name": "TideDefendModel", "variableName": "def_TideDefendModel", "extends": "e", "file": "TideDefendModel.js"}, {"name": "TornadoBullet", "variableName": "_ctor", "extends": "e", "file": "TornadoBullet.js"}, {"name": "exp_TowerAmethystRewardCfgReader", "variableName": "exp_TowerAmethystRewardCfgReader", "extends": "e", "file": "TowerAmethystRewardCfg.js"}, {"name": "exp_TowerCfgReader", "variableName": "exp_TowerCfgReader", "extends": "e", "file": "TowerCfg.js"}, {"name": "exp_TowerCoinRewardCfgReader", "variableName": "exp_TowerCoinRewardCfgReader", "extends": "e", "file": "TowerCoinRewardCfg.js"}, {"name": "exp_TowerLvCfgReader", "variableName": "exp_TowerLvCfgReader", "extends": "e", "file": "TowerLvCfg.js"}, {"name": "exp_TowerMenuCfgReader", "variableName": "exp_TowerMenuCfgReader", "extends": "e", "file": "TowerMenuCfg.js"}, {"name": "TrackBullet", "variableName": "_ctor", "extends": "e", "file": "TrackBullet.js"}, {"name": "TrackItem", "variableName": "_ctor", "extends": "e", "file": "TrackItem.js"}, {"name": "exp_TwoDHorizontalLayoutObject", "variableName": "exp_TwoDHorizontalLayoutObject", "extends": "e", "file": "TwoDHorizontalLayoutObject.js"}, {"name": "exp_TwoDLayoutObject", "variableName": "exp_TwoDLayoutObject", "extends": "e", "file": "TwoDLayoutObject.js"}, {"name": "Vehicle", "variableName": "def_Vehicle", "extends": "e", "file": "Vehicle.js"}, {"name": "VideoButton", "variableName": "_ctor", "extends": "e", "file": "VideoButton.js"}, {"name": "VideoIcon", "variableName": "_ctor", "extends": "e", "file": "VideoIcon.js"}, {"name": "VisibleComponent", "variableName": "_ctor", "extends": "e", "file": "VisibleComponent.js"}, {"name": "WallBase", "variableName": "_ctor", "extends": "e", "file": "WallBase.js"}, {"name": "Weather", "variableName": "_ctor", "extends": "e", "file": "Weather.js"}, {"name": "exp_WeatherCfgReader", "variableName": "exp_WeatherCfgReader", "extends": "e", "file": "WeatherCfg.js"}, {"name": "WebDev", "variableName": "def_WebDev", "extends": "e", "file": "WebDev.js"}], "staticClasses": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "manager", "file": "AlertManager.js"}, {"name": "AudioManager", "type": "manager", "file": "AudioManager.js"}, {"name": "BronMonsterManger", "type": "singleton", "file": "BronMonsterManger.js"}, {"name": "CompManager", "type": "manager", "file": "CompManager.js"}, {"name": "FColliderManager", "type": "manager", "file": "FColliderManager.js"}, {"name": "FightController", "type": "singleton", "file": "FightController.js"}, {"name": "FightModel", "type": "singleton", "file": "FightModel.js"}, {"name": "GameUtil", "type": "manager", "file": "GameUtil.js"}, {"name": "Manager", "type": "manager", "file": "Manager.js"}, {"name": "MathUtils", "type": "manager", "file": "MathUtils.js"}, {"name": "MVC", "type": "singleton", "file": "MVC.js"}, {"name": "NetManager", "type": "manager", "file": "NetManager.js"}, {"name": "NPC", "type": "singleton", "file": "NPC.js"}, {"name": "PayController", "type": "singleton", "file": "PayController.js"}, {"name": "PayModel", "type": "singleton", "file": "PayModel.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "manager", "file": "ResUtil.js"}, {"name": "Role", "type": "singleton", "file": "Role.js"}, {"name": "SkillController", "type": "singleton", "file": "SkillController.js"}, {"name": "SkillManager", "type": "manager", "file": "SkillManager.js"}, {"name": "SkillModel", "type": "singleton", "file": "SkillModel.js"}, {"name": "StorageManager", "type": "manager", "file": "StorageManager.js"}, {"name": "UIManager", "type": "manager", "file": "UIManager.js"}, {"name": "VoManager", "type": "manager", "file": "VoManager.js"}], "exportedFunctions": [{"name": "click", "file": "Api.js"}, {"name": "report", "file": "Api.js"}, {"name": "getOpenid", "file": "Api.js"}, {"name": "serverTime", "file": "Api.js"}, {"name": "initlang", "file": "cc_language.js"}, {"name": "versionFormat", "file": "function.js"}, {"name": "formatDate", "file": "function.js"}, {"name": "response", "file": "function.js"}, {"name": "getSign", "file": "function.js"}, {"name": "getServerTime", "file": "function.js"}, {"name": "cacheFunction", "file": "GameUtil.js"}, {"name": "getClassByName", "file": "GameUtil.js"}, {"name": "isNullOrEmpty", "file": "Global.js"}, {"name": "copy", "file": "Global.js"}, {"name": "toArray", "file": "Global.js"}, {"name": "GameDeepCopy", "file": "Global.js"}, {"name": "iOSSendMsg", "file": "IOSSdk.js"}, {"name": "iOSBuySendMsg", "file": "IOSSdk.js"}, {"name": "md5", "file": "md51.js"}, {"name": "ActivityPass", "file": "ModeBackpackHeroModel.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>au<PERSON><PERSON>", "file": "ModuleLauncher.js"}, {"name": "onpagehide", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "onbeforeunload", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "__dynamicPropertiesForNative", "file": "thinkingdata.mg.cocoscreator.min.js"}, {"name": "UILauncher", "file": "UILauncher.js"}], "enums": [{"name": "AlertType", "file": "AlertManager.js"}, {"name": "ItemAlertType", "file": "AlertManager.js"}, {"name": "AudioType", "file": "AudioManager.js"}, {"name": "PlayType", "file": "AudioManager.js"}, {"name": "CampType", "file": "BaseEntity.js"}, {"name": "EntityType", "file": "BaseEntity.js"}, {"name": "ColliderType", "file": "FCollider.js"}, {"name": "GRID_TYPE", "file": "GridView.js"}, {"name": "MoreGames", "file": "MoreGamesView.js"}, {"name": "NodeState", "file": "NodePool.js"}, {"name": "Hurt", "file": "PropertyVo.js"}, {"name": "RBadge", "file": "RBadgeModel.js"}, {"name": "TaskSaveType", "file": "TaskModel.js"}, {"name": "LAYOUT_HORIZONTAL_TYPE", "file": "TwoDLayoutObject.js"}, {"name": "LAYOUT_VERTICAL_TYPE", "file": "TwoDLayoutObject.js"}], "otherDefinitions": [{"name": "activityCfgReader", "type": "exportedClass", "file": "activityCfg.js"}, {"name": "activityCfg", "type": "config", "file": "activityCfg.js"}, {"name": "ADController", "type": "exportedClass", "file": "ADController.js"}, {"name": "ADModel", "type": "valueObject", "file": "ADModel.js"}, {"name": "adRewardCfgReader", "type": "exportedClass", "file": "adRewardCfg.js"}, {"name": "adRewardCfg", "type": "config", "file": "adRewardCfg.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "exportedClass", "file": "AlertManager.js"}, {"name": "LoadResArgs", "type": "exportedClass", "file": "AssetLoader.js"}, {"name": "AudioManager", "type": "exportedClass", "file": "AudioManager.js"}, {"name": "AutoAmTool", "type": "utility", "file": "AutoAmTool.js"}, {"name": "AutoScaleComponent", "type": "exportedClass", "file": "AutoScaleComponent.js"}, {"name": "BagBuffCfgReader", "type": "exportedClass", "file": "BagBuffCfg.js"}, {"name": "BagBuffCfg", "type": "config", "file": "BagBuffCfg.js"}, {"name": "BagGuideCfgReader", "type": "exportedClass", "file": "BagGuideCfg.js"}, {"name": "BagGuideCfg", "type": "config", "file": "BagGuideCfg.js"}, {"name": "BagModeLvCfgReader", "type": "exportedClass", "file": "BagModeLvCfg.js"}, {"name": "BagModeLvCfg", "type": "config", "file": "BagModeLvCfg.js"}, {"name": "BagModeSkillPoolCfgReader", "type": "exportedClass", "file": "BagModeSkillPoolCfg.js"}, {"name": "BagModeSkillPoolCfg", "type": "config", "file": "BagModeSkillPoolCfg.js"}, {"name": "bagMonsterLvCfgReader", "type": "exportedClass", "file": "bagMonsterLvCfg.js"}, {"name": "bagMonsterLvCfg", "type": "config", "file": "bagMonsterLvCfg.js"}, {"name": "BagShopItemCfgReader", "type": "exportedClass", "file": "BagShopItemCfg.js"}, {"name": "BagShopItemCfg", "type": "config", "file": "BagShopItemCfg.js"}, {"name": "BagSkillCfgReader", "type": "exportedClass", "file": "BagSkillCfg.js"}, {"name": "BagSkillCfg", "type": "config", "file": "BagSkillCfg.js"}, {"name": "BaseUrl", "type": "namespace", "file": "BaseNet.js"}, {"name": "Url", "type": "namespace", "file": "BaseNet.js"}, {"name": "BaseNet", "type": "exportedClass", "file": "BaseNet.js"}, {"name": "VideoAdCode", "type": "namespace", "file": "BaseSdk.js"}, {"name": "ShareType", "type": "namespace", "file": "BaseSdk.js"}, {"name": "BaseSdk", "type": "exportedClass", "file": "BaseSdk.js"}, {"name": "BottomBarController", "type": "exportedClass", "file": "BottomBarController.js"}, {"name": "BottomBarModel", "type": "valueObject", "file": "BottomBarModel.js"}, {"name": "BottomBarView", "type": "exportedClass", "file": "BottomBarView.js"}, {"name": "BoxLevelExpCfgReader", "type": "exportedClass", "file": "BoxLevelExpCfg.js"}, {"name": "BoxLevelExpCfg", "type": "config", "file": "BoxLevelExpCfg.js"}, {"name": "BronMonsterManger", "type": "exportedClass", "file": "BronMonsterManger.js"}, {"name": "Buff", "type": "namespace", "file": "Buff.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "exportedClass", "file": "BuffCfg.js"}, {"name": "BuffCfg", "type": "config", "file": "BuffCfg.js"}, {"name": "BuffController", "type": "exportedClass", "file": "BuffController.js"}, {"name": "<PERSON><PERSON><PERSON>", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_Excute", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_OnTime", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_<PERSON>", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_OnSpawnHurt", "type": "exportedClass", "file": "BuffList.js"}, {"name": "B<PERSON>_OnVampirism", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_OnBehit", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_<PERSON>", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_HPLink", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_Continuous<PERSON><PERSON><PERSON>y", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_HPLinkOnce", "type": "exportedClass", "file": "BuffList.js"}, {"name": "B<PERSON>_EntityDead", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_VicinityHurt", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_<PERSON><PERSON>", "type": "exportedClass", "file": "BuffList.js"}, {"name": "B<PERSON>_<PERSON>", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_OnUseSkillHurt", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_SubSkill", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_AtkFocus", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_AdrenalTechnology", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_OnSkillUseUnload", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_ReboundDam", "type": "exportedClass", "file": "BuffList.js"}, {"name": "B<PERSON>_<PERSON>KillLayout", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_<PERSON><PERSON>", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_OnLifeVal", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_OnSpawnHurtAddArmor", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON>uff_OnBehitAddArmor", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_RestoreArmor", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_<PERSON>kill", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_<PERSON>", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_AssociationProp", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_ResistDamage", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_SetSlash", "type": "exportedClass", "file": "BuffList.js"}, {"name": "Buff_OnRoundState", "type": "exportedClass", "file": "BuffList.js"}, {"name": "<PERSON><PERSON>_<PERSON><PERSON>", "type": "exportedClass", "file": "BuffList.js"}, {"name": "BuffModel", "type": "valueObject", "file": "BuffModel.js"}, {"name": "BuildModeSkiilpoolCfgReader", "type": "exportedClass", "file": "BuildModeSkiilpoolCfg.js"}, {"name": "BuildModeSkiilpoolCfg", "type": "config", "file": "BuildModeSkiilpoolCfg.js"}, {"name": "BulletEffectCfgReader", "type": "exportedClass", "file": "BulletEffectCfg.js"}, {"name": "BulletEffectCfg", "type": "config", "file": "BulletEffectCfg.js"}, {"name": "BulletVo", "type": "exportedClass", "file": "BulletVo.js"}, {"name": "CallID", "type": "namespace", "file": "CallID.js"}, {"name": "RedPointID", "type": "namespace", "file": "CallID.js"}, {"name": "Cfg", "type": "config", "file": "Cfg.js"}, {"name": "CurrencyConfigDefine", "type": "namespace", "file": "CurrencyConfigCfg.js"}, {"name": "CurrencyConfigCfgReader", "type": "exportedClass", "file": "CurrencyConfigCfg.js"}, {"name": "CurrencyConfigCfg", "type": "config", "file": "CurrencyConfigCfg.js"}, {"name": "dmmItemCfgReader", "type": "exportedClass", "file": "dmmItemCfg.js"}, {"name": "dmmItemCfg", "type": "config", "file": "dmmItemCfg.js"}, {"name": "dmmRoleCfgReader", "type": "exportedClass", "file": "dmmRoleCfg.js"}, {"name": "dmmRoleCfg", "type": "config", "file": "dmmRoleCfg.js"}, {"name": "Dragon", "type": "exportedClass", "file": "Dragon.js"}, {"name": "dragonPathCfgReader", "type": "exportedClass", "file": "dragonPathCfg.js"}, {"name": "dragonPathCfg", "type": "config", "file": "dragonPathCfg.js"}, {"name": "DropConfigDefine", "type": "namespace", "file": "DropConfigCfg.js"}, {"name": "DropConfigCfgReader", "type": "exportedClass", "file": "DropConfigCfg.js"}, {"name": "DropConfigCfg", "type": "config", "file": "DropConfigCfg.js"}, {"name": "EaseScaleTransition", "type": "exportedClass", "file": "EaseScaleTransition.js"}, {"name": "EquipLvCfgReader", "type": "exportedClass", "file": "EquipLvCfg.js"}, {"name": "EquipLvCfg", "type": "config", "file": "EquipLvCfg.js"}, {"name": "EquipMergeLvCfgReader", "type": "exportedClass", "file": "EquipMergeLvCfg.js"}, {"name": "EquipMergeLvCfg", "type": "config", "file": "EquipMergeLvCfg.js"}, {"name": "EventController", "type": "exportedClass", "file": "EventController.js"}, {"name": "EventModel", "type": "valueObject", "file": "EventModel.js"}, {"name": "ExchangeCodeView", "type": "view", "file": "ExchangeCodeView.js"}, {"name": "StateType", "type": "namespace", "file": "FCollider.js"}, {"name": "FightController", "type": "exportedClass", "file": "FightController.js"}, {"name": "FightModel", "type": "valueObject", "file": "FightModel.js"}, {"name": "FightScene", "type": "exportedClass", "file": "FightScene.js"}, {"name": "FightUIView", "type": "exportedClass", "file": "FightUIView.js"}, {"name": "arrSort", "type": "exportedClass", "file": "function.js"}, {"name": "getNonce", "type": "exportedClass", "file": "function.js"}, {"name": "getClientTime", "type": "exportedClass", "file": "function.js"}, {"name": "Game", "type": "namespace", "file": "Game.js"}, {"name": "ModeCfg", "type": "exportedClass", "file": "Game.js"}, {"name": "GameatrDefine", "type": "namespace", "file": "GameatrCfg.js"}, {"name": "GameatrCfgReader", "type": "exportedClass", "file": "GameatrCfg.js"}, {"name": "GameatrCfg", "type": "config", "file": "GameatrCfg.js"}, {"name": "GameCamera", "type": "exportedClass", "file": "GameCamera.js"}, {"name": "GameSeting", "type": "namespace", "file": "GameSeting.js"}, {"name": "TMap", "type": "exportedClass", "file": "GameSeting.js"}, {"name": "GameSettingDefine", "type": "namespace", "file": "GameSettingCfg.js"}, {"name": "GameSettingCfgReader", "type": "exportedClass", "file": "GameSettingCfg.js"}, {"name": "GameSettingCfg", "type": "config", "file": "GameSettingCfg.js"}, {"name": "CCTool", "type": "namespace", "file": "GameUtil.js"}, {"name": "GameUtil", "type": "exportedClass", "file": "GameUtil.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "view", "file": "GridView.js"}, {"name": "Grid<PERSON>iew<PERSON>ell", "type": "view", "file": "GridViewCell.js"}, {"name": "GridViewFreshWorkItem", "type": "exportedClass", "file": "GridViewFreshWork.js"}, {"name": "GridViewFreshWork", "type": "exportedClass", "file": "GridViewFreshWork.js"}, {"name": "GuideCfgReader", "type": "exportedClass", "file": "GuideCfg.js"}, {"name": "GuideCfg", "type": "config", "file": "GuideCfg.js"}, {"name": "GuidesController", "type": "exportedClass", "file": "GuidesController.js"}, {"name": "GuidesModel", "type": "valueObject", "file": "GuidesModel.js"}, {"name": "HttpClient", "type": "exportedClass", "file": "HttpClient.js"}, {"name": "Intersection", "type": "exportedClass", "file": "Intersection.js"}, {"name": "AngleSlerp", "type": "exportedClass", "file": "Intersection.js"}, {"name": "ItemController", "type": "exportedClass", "file": "ItemController.js"}, {"name": "ItemModel", "type": "valueObject", "file": "ItemModel.js"}, {"name": "KnapsackVo", "type": "namespace", "file": "KnapsackVo.js"}, {"name": "languageCfgReader", "type": "exportedClass", "file": "languageCfg.js"}, {"name": "languageCfg", "type": "config", "file": "languageCfg.js"}, {"name": "LanguageFun", "type": "exportedClass", "file": "LanguageFun.js"}, {"name": "LatticeMap", "type": "namespace", "file": "LatticeMap.js"}, {"name": "LayoutObject", "type": "exportedClass", "file": "LayoutObject.js"}, {"name": "LevelExpCfgReader", "type": "exportedClass", "file": "LevelExpCfg.js"}, {"name": "LevelExpCfg", "type": "config", "file": "LevelExpCfg.js"}, {"name": "Level", "type": "namespace", "file": "LevelMgr.js"}, {"name": "ListenID", "type": "namespace", "file": "ListenID.js"}, {"name": "LoadingController", "type": "exportedClass", "file": "LoadingController.js"}, {"name": "LoadingModel", "type": "valueObject", "file": "LoadingModel.js"}, {"name": "LoadingView", "type": "exportedClass", "file": "LoadingView.js"}, {"name": "LvInsideCfgReader", "type": "exportedClass", "file": "LvInsideCfg.js"}, {"name": "LvInsideCfg", "type": "config", "file": "LvInsideCfg.js"}, {"name": "LvOutsideCfgReader", "type": "exportedClass", "file": "LvOutsideCfg.js"}, {"name": "LvOutsideCfg", "type": "config", "file": "LvOutsideCfg.js"}, {"name": "M20_Pop_GameRewardView", "type": "view", "file": "M20_Pop_GameRewardView.js"}, {"name": "M20_PrePare_MenuView", "type": "view", "file": "M20_PrePare_MenuView.js"}, {"name": "M33_FightBuffView", "type": "view", "file": "M33_FightBuffView.js"}, {"name": "M33_FightScene", "type": "exportedClass", "file": "M33_FightScene.js"}, {"name": "M33_FightUIView", "type": "exportedClass", "file": "M33_FightUIView.js"}, {"name": "MapCfgReader", "type": "exportedClass", "file": "MapCfg.js"}, {"name": "MapCfg", "type": "config", "file": "MapCfg.js"}, {"name": "MathSection", "type": "exportedClass", "file": "MathSection.js"}, {"name": "MathUtils", "type": "exportedClass", "file": "MathUtils.js"}, {"name": "MBPack", "type": "namespace", "file": "MBackpackHero.js"}, {"name": "MBRebound", "type": "namespace", "file": "MBRebound.js"}, {"name": "MCBoss", "type": "exportedClass", "file": "MCBoss.js"}, {"name": "MCBossState", "type": "namespace", "file": "MCBossState.js"}, {"name": "MCDragoMutilation", "type": "exportedClass", "file": "MCDragoMutilation.js"}, {"name": "MCDragon", "type": "exportedClass", "file": "MCDragon.js"}, {"name": "<PERSON><PERSON><PERSON>", "type": "namespace", "file": "MChains.js"}, {"name": "MiniGameEquipCfgReader", "type": "exportedClass", "file": "MiniGameEquipCfg.js"}, {"name": "MiniGameEquipCfg", "type": "config", "file": "MiniGameEquipCfg.js"}, {"name": "MiniGameLvCfgReader", "type": "exportedClass", "file": "MiniGameLvCfg.js"}, {"name": "MiniGameLvCfg", "type": "config", "file": "MiniGameLvCfg.js"}, {"name": "MinSortList", "type": "exportedClass", "file": "MinSortList.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "namespace", "file": "MMGuards.js"}, {"name": "ModeAllOutAttackController", "type": "exportedClass", "file": "ModeAllOutAttackController.js"}, {"name": "ModeAllOutAttackModel", "type": "valueObject", "file": "ModeAllOutAttackModel.js"}, {"name": "ModeBackpackHeroController", "type": "exportedClass", "file": "ModeBackpackHeroController.js"}, {"name": "ModeBackpackHeroModel", "type": "valueObject", "file": "ModeBackpackHeroModel.js"}, {"name": "ModeBulletsReboundController", "type": "exportedClass", "file": "ModeBulletsReboundController.js"}, {"name": "ModeBulletsReboundModel", "type": "valueObject", "file": "ModeBulletsReboundModel.js"}, {"name": "ModeChainsController", "type": "exportedClass", "file": "ModeChainsController.js"}, {"name": "ModeChainsModel", "type": "valueObject", "file": "ModeChainsModel.js"}, {"name": "ModeDragonWarController", "type": "exportedClass", "file": "ModeDragonWarController.js"}, {"name": "ModeDragonWarModel", "type": "valueObject", "file": "ModeDragonWarModel.js"}, {"name": "ModeManGuardsController", "type": "exportedClass", "file": "ModeManGuardsController.js"}, {"name": "ModeManGuardsModel", "type": "valueObject", "file": "ModeManGuardsModel.js"}, {"name": "ModePickUpBulletsController", "type": "exportedClass", "file": "ModePickUpBulletsController.js"}, {"name": "ModePickUpBulletsModel", "type": "valueObject", "file": "ModePickUpBulletsModel.js"}, {"name": "ModeThrowingKnifeController", "type": "exportedClass", "file": "ModeThrowingKnifeController.js"}, {"name": "ModeThrowingKnifeModel", "type": "valueObject", "file": "ModeThrowingKnifeModel.js"}, {"name": "Monster", "type": "exportedClass", "file": "Monster.js"}, {"name": "MonsterCfgReader", "type": "exportedClass", "file": "MonsterCfg.js"}, {"name": "MonsterCfg", "type": "config", "file": "MonsterCfg.js"}, {"name": "MonsterLvCfgReader", "type": "exportedClass", "file": "MonsterLvCfg.js"}, {"name": "MonsterLvCfg", "type": "config", "file": "MonsterLvCfg.js"}, {"name": "MonsterState", "type": "namespace", "file": "MonsterState.js"}, {"name": "MonsterTidalState", "type": "namespace", "file": "MonsterTidalState.js"}, {"name": "MoreGamesView", "type": "view", "file": "MoreGamesView.js"}, {"name": "MTideDefendRebound", "type": "namespace", "file": "MTideDefendRebound.js"}, {"name": "MTKnife", "type": "namespace", "file": "MTKnife.js"}, {"name": "MVC", "type": "namespace", "file": "MVC.js"}, {"name": "NetManager", "type": "exportedClass", "file": "NetManager.js"}, {"name": "NodePoolItem", "type": "exportedClass", "file": "NodePool.js"}, {"name": "Notifier", "type": "exportedClass", "file": "Notifier.js"}, {"name": "NotifyCaller", "type": "exportedClass", "file": "NotifyCaller.js"}, {"name": "NotifyID", "type": "namespace", "file": "NotifyID.js"}, {"name": "Listener<PERSON><PERSON><PERSON>", "type": "exportedClass", "file": "NotifyListener.js"}, {"name": "NotifyListener", "type": "exportedClass", "file": "NotifyListener.js"}, {"name": "ObjectPool", "type": "exportedClass", "file": "ObjectPool.js"}, {"name": "PayController", "type": "exportedClass", "file": "PayController.js"}, {"name": "PayModel", "type": "valueObject", "file": "PayModel.js"}, {"name": "PayShopCfgReader", "type": "exportedClass", "file": "PayShopCfg.js"}, {"name": "PayShopCfg", "type": "config", "file": "PayShopCfg.js"}, {"name": "PetState", "type": "namespace", "file": "PetState.js"}, {"name": "PoolSpawner", "type": "exportedClass", "file": "Pool.js"}, {"name": "Pool", "type": "exportedClass", "file": "Pool.js"}, {"name": "PoolMgrBase", "type": "exportedClass", "file": "Pool.js"}, {"name": "PoolArray", "type": "exportedClass", "file": "PoolArray.js"}, {"name": "PoolListCfgReader", "type": "exportedClass", "file": "PoolListCfg.js"}, {"name": "PoolListCfg", "type": "config", "file": "PoolListCfg.js"}, {"name": "Pop", "type": "exportedClass", "file": "Pop.js"}, {"name": "ProcessRewardsCfgReader", "type": "exportedClass", "file": "ProcessRewardsCfg.js"}, {"name": "ProcessRewardsCfg", "type": "config", "file": "ProcessRewardsCfg.js"}, {"name": "Property", "type": "namespace", "file": "PropertyVo.js"}, {"name": "PropertyVo", "type": "valueObject", "file": "PropertyVo.js"}, {"name": "QuadTree", "type": "exportedClass", "file": "QuadTree.js"}, {"name": "randomNameCfgReader", "type": "exportedClass", "file": "randomNameCfg.js"}, {"name": "randomNameCfg", "type": "config", "file": "randomNameCfg.js"}, {"name": "RBadgeController", "type": "exportedClass", "file": "RBadgeController.js"}, {"name": "RBadgeModel", "type": "valueObject", "file": "RBadgeModel.js"}, {"name": "RecordVo", "type": "namespace", "file": "RecordVo.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "exportedClass", "file": "ResUtil.js"}, {"name": "RewardEvent", "type": "namespace", "file": "RewardEvent.js"}, {"name": "RoleCfgReader", "type": "exportedClass", "file": "RoleCfg.js"}, {"name": "RoleCfg", "type": "config", "file": "RoleCfg.js"}, {"name": "RoleLvCfgReader", "type": "exportedClass", "file": "RoleLvCfg.js"}, {"name": "RoleLvCfg", "type": "config", "file": "RoleLvCfg.js"}, {"name": "<PERSON><PERSON>_<PERSON><PERSON>ult", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_AtkDefault", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_NormalChop", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_MagicBall", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Multishot", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_FireOffset", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_ParallelFire", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Sniper", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_MultiBullet", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_StruckLightning", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Meteorite", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_RingFireballs", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_RangeBomb", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_LaserRadiation", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_LaserAnacampsis", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Ligature", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_LaserRadiationGuard", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Arrows", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Venom", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Icicle", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_BounceThrowingKnife", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Whirlwind", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Tornado", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Continuous", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_GoldenCudgel", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_SwordSword", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_EffectSkill", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_ColdAir", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_PosExcute", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_StampSweep", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_PowerStorage", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Flower", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_Magazine", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_FollowPath", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "Skill_MCDragonFollowPath", "type": "exportedClass", "file": "RoleSkillList.js"}, {"name": "RoleState", "type": "namespace", "file": "RoleState.js"}, {"name": "RoleUnlockCfgReader", "type": "exportedClass", "file": "RoleUnlockCfg.js"}, {"name": "RoleUnlockCfg", "type": "config", "file": "RoleUnlockCfg.js"}, {"name": "Language", "type": "namespace", "file": "SdkConfig.js"}, {"name": "SdkConfig", "type": "config", "file": "SdkConfig.js"}, {"name": "SdkLauncher", "type": "exportedClass", "file": "SdkLauncher.js"}, {"name": "SettingController", "type": "exportedClass", "file": "SettingController.js"}, {"name": "SettingModel", "type": "valueObject", "file": "SettingModel.js"}, {"name": "SettingView", "type": "exportedClass", "file": "SettingView.js"}, {"name": "ShopController", "type": "exportedClass", "file": "ShopController.js"}, {"name": "ShopModel", "type": "valueObject", "file": "ShopModel.js"}, {"name": "signCfg<PERSON><PERSON><PERSON>", "type": "exportedClass", "file": "signCfg.js"}, {"name": "signCfg", "type": "config", "file": "signCfg.js"}, {"name": "SkiilpoolCfgReader", "type": "exportedClass", "file": "SkiilpoolCfg.js"}, {"name": "SkiilpoolCfg", "type": "config", "file": "SkiilpoolCfg.js"}, {"name": "SkillCfgReader", "type": "exportedClass", "file": "SkillCfg.js"}, {"name": "SkillCfg", "type": "config", "file": "SkillCfg.js"}, {"name": "SkillController", "type": "exportedClass", "file": "SkillController.js"}, {"name": "Skill", "type": "namespace", "file": "SkillManager.js"}, {"name": "SkillModel", "type": "valueObject", "file": "SkillModel.js"}, {"name": "BaseSkill", "type": "exportedClass", "file": "SkillModule.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "exportedClass", "file": "Smoother.js"}, {"name": "SoundDefine", "type": "namespace", "file": "SoundCfg.js"}, {"name": "SoundCfgReader", "type": "exportedClass", "file": "SoundCfg.js"}, {"name": "SoundCfg", "type": "config", "file": "SoundCfg.js"}, {"name": "State", "type": "namespace", "file": "StateMachine.js"}, {"name": "StorageID", "type": "namespace", "file": "StorageID.js"}, {"name": "StorageManager", "type": "exportedClass", "file": "StorageManager.js"}, {"name": "SwitchVo", "type": "exportedClass", "file": "SwitchVo.js"}, {"name": "TaskCfgReader", "type": "exportedClass", "file": "TaskCfg.js"}, {"name": "TaskCfg", "type": "config", "file": "TaskCfg.js"}, {"name": "TaskModel", "type": "valueObject", "file": "TaskModel.js"}, {"name": "TaskTypeCfgReader", "type": "exportedClass", "file": "TaskTypeCfg.js"}, {"name": "TaskTypeCfg", "type": "config", "file": "TaskTypeCfg.js"}, {"name": "TConfig", "type": "exportedClass", "file": "TConfig.js"}, {"name": "TestController", "type": "exportedClass", "file": "TestController.js"}, {"name": "TestModel", "type": "valueObject", "file": "TestModel.js"}, {"name": "TestView", "type": "exportedClass", "file": "TestView.js"}, {"name": "TideDefendController", "type": "exportedClass", "file": "TideDefendController.js"}, {"name": "TideDefendModel", "type": "valueObject", "file": "TideDefendModel.js"}, {"name": "TimeDelay", "type": "exportedClass", "file": "Time.js"}, {"name": "TowerAmethystRewardCfgReader", "type": "exportedClass", "file": "TowerAmethystRewardCfg.js"}, {"name": "TowerAmethystRewardCfg", "type": "config", "file": "TowerAmethystRewardCfg.js"}, {"name": "TowerCfgReader", "type": "exportedClass", "file": "TowerCfg.js"}, {"name": "TowerCfg", "type": "config", "file": "TowerCfg.js"}, {"name": "TowerCoinRewardCfgReader", "type": "exportedClass", "file": "TowerCoinRewardCfg.js"}, {"name": "TowerCoinRewardCfg", "type": "config", "file": "TowerCoinRewardCfg.js"}, {"name": "TowerLvCfgReader", "type": "exportedClass", "file": "TowerLvCfg.js"}, {"name": "TowerLvCfg", "type": "config", "file": "TowerLvCfg.js"}, {"name": "TowerMenuCfgReader", "type": "exportedClass", "file": "TowerMenuCfg.js"}, {"name": "TowerMenuCfg", "type": "config", "file": "TowerMenuCfg.js"}, {"name": "TrackManger", "type": "namespace", "file": "TrackManger.js"}, {"name": "TwoDHorizontalLayoutObject", "type": "exportedClass", "file": "TwoDHorizontalLayoutObject.js"}, {"name": "TwoDLayoutObject", "type": "exportedClass", "file": "TwoDLayoutObject.js"}, {"name": "UIManager", "type": "exportedClass", "file": "UIManager.js"}, {"name": "DailyData", "type": "exportedClass", "file": "UserVo.js"}, {"name": "UserVo", "type": "exportedClass", "file": "UserVo.js"}, {"name": "VoManager", "type": "exportedClass", "file": "VoManager.js"}, {"name": "Watcher", "type": "exportedClass", "file": "Watcher.js"}, {"name": "WeatherCfgReader", "type": "exportedClass", "file": "WeatherCfg.js"}, {"name": "WeatherCfg", "type": "config", "file": "WeatherCfg.js"}, {"name": "WonderSdk", "type": "exportedClass", "file": "WonderSdk.js"}]}}