const fs = require('fs');
const path = require('path');

class JSDefinitionAnalyzer {
    constructor() {
        this.results = {
            ccclasses: [],
            staticClasses: [],
            exportedFunctions: [],
            enums: [],
            otherDefinitions: []
        };
    }

    analyzeDirectory(dirPath) {
        const files = this.getAllJSFiles(dirPath);
        console.log(`Found ${files.length} JavaScript files to analyze...`);
        
        files.forEach(file => {
            try {
                this.analyzeFile(file);
            } catch (error) {
                console.error(`Error analyzing ${file}:`, error.message);
            }
        });
        
        return this.results;
    }

    getAllJSFiles(dirPath) {
        const files = [];
        
        function traverse(currentPath) {
            const items = fs.readdirSync(currentPath);
            
            items.forEach(item => {
                if (item.endsWith('.meta')) return; // Skip meta files
                
                const fullPath = path.join(currentPath, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    traverse(fullPath);
                } else if (item.endsWith('.js')) {
                    files.push(fullPath);
                }
            });
        }
        
        traverse(dirPath);
        return files;
    }

    analyzeFile(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const relativePath = path.relative('assets/_script', filePath);
        
        // Analyze ccclass definitions
        this.findCCClasses(content, relativePath);
        
        // Analyze static classes
        this.findStaticClasses(content, relativePath);
        
        // Analyze exported functions
        this.findExportedFunctions(content, relativePath);
        
        // Analyze enums
        this.findEnums(content, relativePath);
        
        // Analyze other definitions
        this.findOtherDefinitions(content, relativePath);
    }

    findCCClasses(content, filePath) {
        // Pattern for compiled ccclass with cc__decorate
        const decoratePattern = /(\w+)\s*=\s*cc__decorate\s*\(\s*\[\s*ccp_ccclass/g;
        
        // Pattern for extends relationship
        const extendsPattern = /cc__extends\s*\(\s*(\w+)\s*,\s*(\w+)\s*\)/g;
        
        let match;
        let extendsMap = new Map();
        
        // Collect extends relationships
        while ((match = extendsPattern.exec(content)) !== null) {
            extendsMap.set(match[1], match[2]);
        }
        
        // Find ccclass decorations
        while ((match = decoratePattern.exec(content)) !== null) {
            const className = match[1];
            const extendsClass = extendsMap.get(className);
            
            this.results.ccclasses.push({
                name: className,
                extends: extendsClass || null,
                file: filePath
            });
        }
    }

    findStaticClasses(content, filePath) {
        // Look for manager/utility classes
        const patterns = [
            /(?:var\s+|exports\.)(\w*[Mm]anager)\s*=/g,
            /(?:var\s+|exports\.)(\w*[Uu]til)\s*=/g,
            /(?:var\s+|exports\.)(\w*[Hh]elper)\s*=/g,
            /window\.(\w+)\s*=\s*{/g
        ];
        
        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                this.results.staticClasses.push({
                    name: match[1],
                    file: filePath
                });
            }
        });
    }

    findExportedFunctions(content, filePath) {
        const patterns = [
            /exports\.(\w+)\s*=\s*function/g,
            /window\.(\w+)\s*=\s*function/g
        ];
        
        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                this.results.exportedFunctions.push({
                    name: match[1],
                    file: filePath
                });
            }
        });
    }

    findEnums(content, filePath) {
        // Pattern for TypeScript-style enum compilation
        const enumPattern = /\(function\s*\(\s*\w+\s*\)\s*{[\s\S]*?}\)\s*\(\s*\w+\s*=\s*exports\.(\w+)\s*\|\|/g;
        
        let match;
        while ((match = enumPattern.exec(content)) !== null) {
            this.results.enums.push({
                name: match[1],
                file: filePath
            });
        }
    }

    findOtherDefinitions(content, filePath) {
        const patterns = [
            { pattern: /(?:var\s+|exports\.)(\w*[Cc]fg)\s*=/g, type: 'config' },
            { pattern: /(?:var\s+|exports\.)(\w*[Vv]o)\s*=/g, type: 'valueObject' },
            { pattern: /(?:var\s+|exports\.)(\w*ID)\s*=/g, type: 'id' },
            { pattern: /(?:var\s+|const\s+)([A-Z_][A-Z0-9_]*)\s*=/g, type: 'constant' }
        ];
        
        patterns.forEach(({ pattern, type }) => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                this.results.otherDefinitions.push({
                    name: match[1],
                    type: type,
                    file: filePath
                });
            }
        });
    }

    generateReport() {
        const report = {
            summary: {
                totalFiles: new Set([
                    ...this.results.ccclasses.map(c => c.file),
                    ...this.results.staticClasses.map(c => c.file),
                    ...this.results.exportedFunctions.map(f => f.file),
                    ...this.results.enums.map(e => e.file),
                    ...this.results.otherDefinitions.map(d => d.file)
                ]).size,
                ccclassCount: this.results.ccclasses.length,
                staticClassCount: this.results.staticClasses.length,
                exportedFunctionCount: this.results.exportedFunctions.length,
                enumCount: this.results.enums.length,
                otherDefinitionCount: this.results.otherDefinitions.length
            },
            details: this.results
        };
        
        return report;
    }
}

// Main execution
const analyzer = new JSDefinitionAnalyzer();
const results = analyzer.analyzeDirectory('assets/_script');
const report = analyzer.generateReport();

// Write report to JSON file
fs.writeFileSync('js_definitions_report_v2.json', JSON.stringify(report, null, 2));

console.log('\n=== JavaScript Definition Analysis Report ===');
console.log(`Total files analyzed: ${report.summary.totalFiles}`);
console.log(`CCClass definitions: ${report.summary.ccclassCount}`);
console.log(`Static classes: ${report.summary.staticClassCount}`);
console.log(`Exported functions: ${report.summary.exportedFunctionCount}`);
console.log(`Enums: ${report.summary.enumCount}`);
console.log(`Other definitions: ${report.summary.otherDefinitionCount}`);
console.log('\nDetailed report saved to: js_definitions_report_v2.json');
