const fs = require('fs');
const path = require('path');

class JSDefinitionAnalyzer {
    constructor() {
        this.results = {
            ccclasses: [],
            staticClasses: [],
            exportedFunctions: [],
            enums: [],
            otherDefinitions: []
        };
    }

    analyzeDirectory(dirPath) {
        const files = this.getAllJSFiles(dirPath);
        console.log(`Found ${files.length} JavaScript files to analyze...`);
        
        files.forEach(file => {
            try {
                this.analyzeFile(file);
            } catch (error) {
                console.error(`Error analyzing ${file}:`, error.message);
            }
        });
        
        return this.results;
    }

    getAllJSFiles(dirPath) {
        const files = [];
        
        function traverse(currentPath) {
            const items = fs.readdirSync(currentPath);
            
            items.forEach(item => {
                if (item.endsWith('.meta')) return; // Skip meta files
                
                const fullPath = path.join(currentPath, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    traverse(fullPath);
                } else if (item.endsWith('.js')) {
                    files.push(fullPath);
                }
            });
        }
        
        traverse(dirPath);
        return files;
    }

    analyzeFile(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const relativePath = path.relative('assets/_script', filePath);
        
        // Analyze ccclass definitions
        this.findCCClasses(content, relativePath);
        
        // Analyze static classes
        this.findStaticClasses(content, relativePath);
        
        // Analyze exported functions
        this.findExportedFunctions(content, relativePath);
        
        // Analyze enums
        this.findEnums(content, relativePath);
        
        // Analyze other definitions
        this.findOtherDefinitions(content, relativePath);
    }

    findCCClasses(content, filePath) {
        // Pattern for compiled ccclass with cc__decorate
        const decoratePattern = /(\w+)\s*=\s*cc__decorate\s*\(\s*\[\s*ccp_ccclass(?:\s*\([^)]*\))?\s*\]/g;

        // Pattern for ccclass function definition
        const ccclassFunctionPattern = /var\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*{[\s\S]*?cc__extends\s*\(\s*\w+\s*,\s*(\w+)\s*\)/g;

        // Pattern for extends relationship in compiled code
        const extendsPattern = /cc__extends\s*\(\s*(\w+)\s*,\s*(\w+)\s*\)/g;

        // Pattern for component class definition
        const componentPattern = /var\s+(\w+)\s*=\s*function\s*\([^)]*\)\s*{[\s\S]*?}\s*;\s*\w+\s*=\s*cc__decorate/g;

        let match;
        let extendsMap = new Map();

        // First pass: collect extends relationships
        while ((match = extendsPattern.exec(content)) !== null) {
            extendsMap.set(match[1], match[2]);
        }

        // Find ccclass decorations
        while ((match = decoratePattern.exec(content)) !== null) {
            const className = match[1];
            const extendsClass = extendsMap.get(className);

            this.results.ccclasses.push({
                name: className,
                extends: extendsClass || null,
                file: filePath,
                type: 'decorated'
            });
        }

        // Find component classes
        while ((match = componentPattern.exec(content)) !== null) {
            const className = match[1];
            const extendsClass = extendsMap.get(className);

            // Check if it's already added by decoration pattern
            const exists = this.results.ccclasses.some(c => c.name === className && c.file === filePath);
            if (!exists) {
                this.results.ccclasses.push({
                    name: className,
                    extends: extendsClass || null,
                    file: filePath,
                    type: 'component'
                });
            }
        }

        // Find function-based ccclass definitions
        while ((match = ccclassFunctionPattern.exec(content)) !== null) {
            const className = match[1];
            const extendsClass = match[2];

            // Check if it's already added
            const exists = this.results.ccclasses.some(c => c.name === className && c.file === filePath);
            if (!exists) {
                this.results.ccclasses.push({
                    name: className,
                    extends: extendsClass,
                    file: filePath,
                    type: 'function'
                });
            }
        }
    }

    findStaticClasses(content, filePath) {
        // Pattern for manager/utility classes
        const managerPattern = /(?:var\s+|exports\.)(\w*[Mm]anager|\w*[Uu]til|\w*[Hh]elper|\w*[Ss]ervice)\s*=\s*{/g;

        // Pattern for singleton classes
        const singletonPattern = /(?:var\s+|exports\.)(\w+)\s*=\s*{[\s\S]*?getInstance[\s\S]*?}/g;

        // Pattern for namespace objects with methods
        const namespacePattern = /(?:var\s+|exports\.)([A-Z]\w+)\s*=\s*{[\s\S]*?function[\s\S]*?}/g;

        // Pattern for static utility objects
        const utilityPattern = /window\.(\w+)\s*=\s*{[\s\S]*?}/g;

        let match;
        const found = new Set();

        // Find manager classes
        while ((match = managerPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.staticClasses.push({
                    name: match[1],
                    file: filePath,
                    type: 'manager'
                });
            }
        }

        // Find singleton classes
        while ((match = singletonPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.staticClasses.push({
                    name: match[1],
                    file: filePath,
                    type: 'singleton'
                });
            }
        }

        // Find namespace objects
        while ((match = namespacePattern.exec(content)) !== null) {
            if (!found.has(match[1]) && this.hasMultipleMethods(content, match[1])) {
                found.add(match[1]);
                this.results.staticClasses.push({
                    name: match[1],
                    file: filePath,
                    type: 'namespace'
                });
            }
        }

        // Find utility objects on window
        while ((match = utilityPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.staticClasses.push({
                    name: match[1],
                    file: filePath,
                    type: 'utility'
                });
            }
        }
    }

    hasMultipleMethods(content, name) {
        // Check if the object has multiple methods
        const methodPattern = new RegExp(`${name}\\s*\\.\\s*\\w+\\s*=\\s*function|${name}\\s*\\[\\s*["']\\w+["']\\s*\\]\\s*=\\s*function`, 'g');
        const matches = content.match(methodPattern);
        return matches && matches.length >= 2;
    }

    findExportedFunctions(content, filePath) {
        // Pattern for exported functions
        const exportFunctionPattern = /exports\.(\w+)\s*=\s*function/g;

        // Pattern for window functions
        const windowFunctionPattern = /window\.(\w+)\s*=\s*function/g;

        // Pattern for module.exports functions
        const moduleExportPattern = /module\.exports\s*=\s*function\s+(\w+)|module\.exports\s*=\s*(\w+)/g;

        // Pattern for direct function exports
        const directExportPattern = /exports\s*=\s*function\s+(\w+)/g;

        let match;
        const found = new Set();

        // Find exports.functionName = function
        while ((match = exportFunctionPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.exportedFunctions.push({
                    name: match[1],
                    file: filePath,
                    type: 'exports'
                });
            }
        }

        // Find window.functionName = function
        while ((match = windowFunctionPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.exportedFunctions.push({
                    name: match[1],
                    file: filePath,
                    type: 'window'
                });
            }
        }

        // Find module.exports functions
        while ((match = moduleExportPattern.exec(content)) !== null) {
            const name = match[1] || match[2];
            if (name && !found.has(name)) {
                found.add(name);
                this.results.exportedFunctions.push({
                    name: name,
                    file: filePath,
                    type: 'module'
                });
            }
        }

        // Find direct exports
        while ((match = directExportPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.exportedFunctions.push({
                    name: match[1],
                    file: filePath,
                    type: 'direct'
                });
            }
        }
    }

    findEnums(content, filePath) {
        // Pattern for TypeScript-style enum compilation
        const tsEnumPattern = /\(function\s*\(\s*\w+\s*\)\s*{[\s\S]*?}\)\s*\(\s*\w+\s*=\s*exports\.(\w+)\s*\|\|\s*\(exports\.(\w+)\s*=\s*{}\)\s*\)/g;

        // Pattern for enum-like objects with numeric values
        const numericEnumPattern = /(?:var\s+|exports\.)(\w+)\s*=\s*{[^}]*\w+\s*:\s*\d+[^}]*}/g;

        // Pattern for enum definitions in function scope
        const functionEnumPattern = /\(function\s*\(\s*(\w+)\s*\)\s*{[\s\S]*?(\w+)\[(\w+)\.(\w+)\s*=\s*\d+\][\s\S]*?}\)/g;

        // Pattern for state/type enums
        const stateEnumPattern = /(\w*[Ss]tate|\w*[Tt]ype|\w*[Mm]ode)\s*\|\|\s*\(\s*exports\.(\w*[Ss]tate|\w*[Tt]ype|\w*[Mm]ode)\s*=\s*{}\s*\)/g;

        let match;
        const found = new Set();

        // Find TypeScript-style enums
        while ((match = tsEnumPattern.exec(content)) !== null) {
            const enumName = match[1] || match[2];
            if (!found.has(enumName)) {
                found.add(enumName);
                this.results.enums.push({
                    name: enumName,
                    file: filePath,
                    type: 'typescript'
                });
            }
        }

        // Find numeric enums
        while ((match = numericEnumPattern.exec(content)) !== null) {
            if (!found.has(match[1]) && this.isNumericEnum(content, match[1])) {
                found.add(match[1]);
                this.results.enums.push({
                    name: match[1],
                    file: filePath,
                    type: 'numeric'
                });
            }
        }

        // Find function-scoped enums
        while ((match = functionEnumPattern.exec(content)) !== null) {
            const enumName = match[1];
            if (!found.has(enumName)) {
                found.add(enumName);
                this.results.enums.push({
                    name: enumName,
                    file: filePath,
                    type: 'function'
                });
            }
        }

        // Find state/type enums
        while ((match = stateEnumPattern.exec(content)) !== null) {
            const enumName = match[1] || match[2];
            if (!found.has(enumName)) {
                found.add(enumName);
                this.results.enums.push({
                    name: enumName,
                    file: filePath,
                    type: 'state'
                });
            }
        }
    }

    isNumericEnum(content, name) {
        // Check if object contains multiple numeric values
        const enumValuePattern = new RegExp(`${name}\\s*=\\s*{[^}]*\\w+\\s*:\\s*\\d+[^}]*\\w+\\s*:\\s*\\d+[^}]*}`, 'g');
        return enumValuePattern.test(content);
    }

    findOtherDefinitions(content, filePath) {
        // Pattern for configuration objects
        const configPattern = /(?:var\s+|exports\.)(\w*[Cc]fg|\w*[Cc]onfig|\w*[Ss]etting)\s*=/g;

        // Pattern for data objects/models
        const dataPattern = /(?:var\s+|exports\.)(\w*[Dd]ata|\w*[Mm]odel|\w*[Vv]o)\s*=/g;

        // Pattern for constants
        const constantPattern = /(?:var\s+|const\s+|let\s+)([A-Z_][A-Z0-9_]*)\s*=/g;

        // Pattern for ID definitions
        const idPattern = /(?:var\s+|exports\.)(\w*ID|\w*Id)\s*=/g;

        // Pattern for adapter/wrapper classes
        const adapterPattern = /(?:var\s+|exports\.)(\w*[Aa]dapter|\w*[Ww]rapper|\w*[Pp]roxy)\s*=/g;

        let match;
        const found = new Set();

        // Find configuration objects
        while ((match = configPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.otherDefinitions.push({
                    name: match[1],
                    type: 'config',
                    file: filePath
                });
            }
        }

        // Find data objects
        while ((match = dataPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.otherDefinitions.push({
                    name: match[1],
                    type: 'data',
                    file: filePath
                });
            }
        }

        // Find constants
        while ((match = constantPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.otherDefinitions.push({
                    name: match[1],
                    type: 'constant',
                    file: filePath
                });
            }
        }

        // Find ID definitions
        while ((match = idPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.otherDefinitions.push({
                    name: match[1],
                    type: 'id',
                    file: filePath
                });
            }
        }

        // Find adapter classes
        while ((match = adapterPattern.exec(content)) !== null) {
            if (!found.has(match[1])) {
                found.add(match[1]);
                this.results.otherDefinitions.push({
                    name: match[1],
                    type: 'adapter',
                    file: filePath
                });
            }
        }
    }

    generateReport() {
        const report = {
            summary: {
                totalFiles: new Set([
                    ...this.results.ccclasses.map(c => c.file),
                    ...this.results.staticClasses.map(c => c.file),
                    ...this.results.exportedFunctions.map(f => f.file),
                    ...this.results.enums.map(e => e.file),
                    ...this.results.otherDefinitions.map(d => d.file)
                ]).size,
                ccclassCount: this.results.ccclasses.length,
                staticClassCount: this.results.staticClasses.length,
                exportedFunctionCount: this.results.exportedFunctions.length,
                enumCount: this.results.enums.length,
                otherDefinitionCount: this.results.otherDefinitions.length
            },
            details: this.results
        };
        
        return report;
    }
}

// Main execution
const analyzer = new JSDefinitionAnalyzer();
const results = analyzer.analyzeDirectory('assets/_script');
const report = analyzer.generateReport();

// Write report to JSON file
fs.writeFileSync('js_definitions_report.json', JSON.stringify(report, null, 2));

console.log('\n=== JavaScript Definition Analysis Report ===');
console.log(`Total files analyzed: ${report.summary.totalFiles}`);
console.log(`CCClass definitions: ${report.summary.ccclassCount}`);
console.log(`Static classes: ${report.summary.staticClassCount}`);
console.log(`Exported functions: ${report.summary.exportedFunctionCount}`);
console.log(`Enums: ${report.summary.enumCount}`);
console.log(`Other definitions: ${report.summary.otherDefinitionCount}`);
console.log('\nDetailed report saved to: js_definitions_report.json');
